const Joi = require('joi');

const { componentType } = require('../../../config/component.constant');

class ChallengeValidation {
    publishChallengeValidation(params) {
        const schema = Joi.object({
            title: Joi.string().required().messages({
                'string.base': 'The title must be a text string.',
                'string.empty': 'The title cannot be empty.',
                'any.required': 'Please provide a title for the component.'
            }),
            image_url: Joi.string().optional().allow(null, '').messages({
                'string.base': 'Please select a valid preview image for your challenge.',
                'string.empty': 'Please select a valid preview image for your challenge.',
                'any.required': 'Please select a valid preview image for your challenge.'
            }),
            description: Joi.string().required().allow(null, '').messages({
                'string.base': 'The short description must be a text string.',
                'any.required': 'A short description is required. It can be empty, but the field must be present.'
            }),
            challenge_type: Joi.string().required().messages({
                'string.base': 'The challenge type must be a valid string.',
                'any.required': 'Please specify the challenge type.'
            }),
            identification_tag: Joi.array().items(Joi.string().trim()).unique().messages({
                'array.base': 'Tags must be an array of strings.',
                'array.unique': 'Tags must be unique.'
            }).optional(),
            start_date: Joi.date()
                .iso()
                .required()
                .messages({
                    'any.required': 'Start date is required.',
                    'date.base': 'Start date must be a valid date.',
                    'date.format': 'Start date must be in ISO 8601 format (e.g. 2025-08-08T00:00:00.000Z).',
                    'date.isoDate': 'Start date must be in ISO 8601 format.'
                }),

            end_date: Joi.date()
                .iso()
                .min(Joi.ref('start_date'))
                .required()
                .messages({
                    'any.required': 'End date is required.',
                    'date.base': 'End date must be a valid date.',
                    'date.min': 'End date must be the same as or after the start date.',
                    'date.format': 'End date must be in ISO 8601 format (e.g. 2025-08-20T23:59:59.999Z).',
                    'date.isoDate': 'End date must be in ISO 8601 format.'
                }),

            result_date: Joi.date()
                .iso()
                .min(Joi.ref('end_date'))
                .required()
                .messages({
                    'any.required': 'Result date is required.',
                    'date.base': 'Result date must be a valid date.',
                    'date.min': 'Result date must be the same as or after the end date.',
                    'date.format': 'Result date must be in ISO 8601 format (e.g. 2025-08-25T23:59:59.999Z).',
                    'date.isoDate': 'Result date must be in ISO 8601 format.'
                })
        }).options({ allowUnknown: true });

        return schema.validate(params);
    }

    createChallengePlaceholder(params) {
        const schema = Joi.object({
            challenge_type: Joi.string().valid(componentType.ELEMENTS, componentType.REPOSITORY, componentType.MOBILE).required(),
            category_id: Joi.string().optional()
        }).options({ allowUnknown: true });
        return schema.validate(params);
    }
}

module.exports = new ChallengeValidation();