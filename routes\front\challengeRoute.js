const express = require('express');
const router = express.Router();

const { createChallengePlaceHolder, getDraftChallengeDetails, updateChallenge, publishDraftChallenges } = require('../../controller/front/challenge.controller');

const { createChallengePlaceholderValidation } = require('../../middlewares/validations/front/challenge/challengeValidation');

router.post('/create/placeholder', createChallengePlaceholderValidation, createChallengePlaceHolder);
router.get('/get-challenge/draft/:id', getDraftChallengeDetails);
router.put('/update/:id', updateChallenge);
router.post('/:id/publish', publishDraftChallenges);

module.exports = router;