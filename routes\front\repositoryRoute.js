const express = require('express');
const router = express.Router();
const { getRepositoryContent, getRepositoryCommitInfo, getRepositoryFileContent, downloadRepositoryContent, getRepositoryNameAvailability, getRepositoryBranches, createRepositoryCommit, downloadRepositoryFileContent, fetchRepositoryDetails, getRepositoryCommitDiff, getRepositoryContentRecursively, uploadMultipleFilesToRepository, createBranch, createMergeRequest, listMergeRequest, approveMergeRequest, addCommentToMergeRequest, getRepositoryMembers, getMergeRequestDetails, getMergeRequestChanges, getMergeRequestCommits, getMergeRequestDifferences, getBranchCommitsDifferences, getBranchFilesDifferences, closeMergeRequest, getMergeRequestActivity, uploadZipToRepository, getMergeRequestDifferencesList, getRepositoryStatistics, deleteBranch, getBranchCommitsDifferenceCounts } = require('../../controller/front/repository.controller');

const { addCommentToMergeRequestValidation } = require('./../../middlewares/validations/front/merge_request/mergeRequestValidation');
const { createCommitValidation, createCommitWithFilesValidation, createBranchValidation, createMergeRequestValidation, deleteBranchValidation } = require('../../middlewares/validations/front/repository/repositoryValidation');

const { validateOwnership } = require('../../middlewares/validateCodeSpace');

router.get('/get-content/:id', validateOwnership, getRepositoryContent);
router.get('/get-commits/:id', validateOwnership, getRepositoryCommitInfo);
router.get('/get-file-content/:id/:path/raw', validateOwnership, getRepositoryFileContent);
router.get('/download/:id', validateOwnership, downloadRepositoryContent);
router.get('/check-name-availability', validateOwnership, getRepositoryNameAvailability);
router.post('/submit-commit/:id', validateOwnership, createCommitValidation, createRepositoryCommit);
router.get('/get-details/:id', validateOwnership, fetchRepositoryDetails);
router.get('/download/file/:id', validateOwnership, downloadRepositoryFileContent);
router.get('/:id/commit/:sha/diff', validateOwnership, getRepositoryCommitDiff);
router.get('/get-content/recursive/:id', validateOwnership, getRepositoryContentRecursively);
router.post('/upload-files/multiple/:id', validateOwnership, createCommitWithFilesValidation, uploadMultipleFilesToRepository);
router.get('/get-members/:id', validateOwnership, getRepositoryMembers);
router.get('/get-branches/:id', validateOwnership, getRepositoryBranches);
router.post('/create-branch/:id', validateOwnership, createBranchValidation, createBranch);
router.post('/:id/merge-request/create', validateOwnership, createMergeRequestValidation, createMergeRequest);
router.get('/:id/merge-request/list', validateOwnership, listMergeRequest);
router.get('/:id/merge-request/:merge_request_id', validateOwnership, getMergeRequestDetails);
router.post('/:id/merge-request/:merge_request_id/approve', validateOwnership, approveMergeRequest);
router.post('/:id/merge-request/:merge_request_id/comments', validateOwnership, addCommentToMergeRequestValidation, addCommentToMergeRequest);
router.get('/:id/merge-request/:merge_request_id/changes', validateOwnership, getMergeRequestChanges);
router.get('/:id/merge-request/:merge_request_id/commits', validateOwnership, getMergeRequestCommits);
router.get('/:id/merge-request/:merge_request_id/diffs', validateOwnership, getMergeRequestDifferences);
router.get('/:id/get-branches/commits/diffs', validateOwnership, getBranchCommitsDifferences);
router.get('/:id/get-branches/files/diffs', validateOwnership, getBranchFilesDifferences);
router.put('/:id/merge-request/:merge_request_id/close', validateOwnership, closeMergeRequest);
router.get('/:id/merge-request/:merge_request_id/activity', validateOwnership, getMergeRequestActivity);
router.post('/:id/upload-zip', validateOwnership, createCommitWithFilesValidation, uploadZipToRepository);
router.get('/:id/merge-request/:merge_request_id/diffs/list', validateOwnership, getMergeRequestDifferencesList);
router.get('/get-statistics/:id', validateOwnership, getRepositoryStatistics);
router.put('/delete-branch/:id', validateOwnership, deleteBranchValidation, deleteBranch);
router.get('/:id/get-branches/commits/diffs/count-only', validateOwnership, getBranchCommitsDifferenceCounts);

module.exports = router;