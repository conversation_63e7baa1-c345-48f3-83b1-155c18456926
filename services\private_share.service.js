const crypto = require('crypto');
const mongoose = require('mongoose');
const moment = require('moment');

// Models
const {
    ComponentPrivateShares
} = require('../models/component_private_shares.model');
const { Components } = require('../models/component.model');
const { Users } = require('../models/users.model');
const {
    ComponentUnlockHistory
} = require('../models/component_unlock_history.model');

// Services
const {
    sendPrivateShareInvitationEmail,
    sendPrivateShareAcceptedNotification
} = require('./send_email.service');
const logger = require('../config/logger');

// Constants
const {
    componentState,
    privateShareStatus,
    accessType: ACCESS_TYPE,
    filterTypes,
    shareType,
    accessDuration: ACCESS_DURATION
} = require('../config/component.constant');
const constants = require('../config/constants');
const { escapeRegex } = require('./general.helper');
/**
 * Generate a secure access token for private sharing
 */
function generateAccessToken() {
    return crypto.randomBytes(32).toString('hex');
}

/**
 * Generate a public shareable link for project
 */
async function generatePublicShareableLink(
    componentId,
    sharedBy,
    linkName,
    accessDuration = 'undefined',
    durationDays = null,
    accessControls = [],
    frontendUrl = null,
    is_regenerate = false
) {
    try {
        // Validate component exists, user owns it, and it's private
        const component = await Components.findOne({
            _id: componentId,
            created_by_user: sharedBy,
            component_state: componentState.PRIVATE
        }).lean();

        if (!component) {
            throw new Error(
                'Project not found, you do not have permission to share it, or project must be private to share privately'
            );
        }

        // Define reusable query for existing shares
        const existingShareQuery = {
            component_id: componentId,
            shared_by: sharedBy,
            access_type: shareType.BY_LINK,
            is_active: true,
            is_master: true
        };

        // Find the share document
        const existingShare = await ComponentPrivateShares.findOne(existingShareQuery).lean();

        if (existingShare) {
            if (!is_regenerate) {
                throw new Error(
                    'A shareable link already exists for this project. Please use the existing link'
                );
            }

            // Delete existing share when regenerating
            await ComponentPrivateShares.deleteOne({ _id: existingShare._id });
            logger.info(`Deleted existing share ${existingShare._id} for regeneration`);
        }

        const accessToken = generateAccessToken();
        const expiresAt =
            accessDuration === ACCESS_DURATION.DAYS && durationDays
                ? moment.utc().add(durationDays, ACCESS_DURATION.DAYS).set({
                    hour: 23,
                    minute: 59,
                    second: 59,
                    millisecond: 0
                }).toDate() : null;

        const shareData = {
            component_id: componentId,
            shared_by: sharedBy,
            access_type: shareType.BY_LINK,
            access_token: accessToken,
            expires_at: expiresAt,
            access_duration: accessDuration,
            duration_days: durationDays,
            access_controls: accessControls,
            link_name: linkName,
            status: privateShareStatus.ACTIVE, // Multi-use links start as active
            // Shareable links remain unassigned - multiple users can access
            shared_with_user: null,
            shared_with_email: null,
            access_count: 0, // Track total access count
            is_master: true, // This is the master record for the shareable link
            master_share_id: null // Master records don't reference themselves
        };

        const share = await ComponentPrivateShares.create(shareData);

        // Generate shareable URL
        let shareableUrl;
        if (frontendUrl) {
            const separator = frontendUrl.includes('?') ? '&' : '?';
            shareableUrl = `${frontendUrl}${separator}token=${accessToken}`;
        }

        return {
            success: true,
            share,
            shareableUrl: shareableUrl
        };
    } catch (error) {
        logger.error(`Error in generatePublicShareableLink: ${error.message}`);
        throw error;
    }
}

/**
 * Validate email format
 */
function isValidEmail(recipientEmail) {

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

    return emailRegex.test(recipientEmail);
}

/**
 * Access multi-use shareable link
 */
async function accessShareableLink(accessToken, userId, userEmail, userAgent = null, ipAddress = null) {
    try {
        // Find the master shareable link record
        const masterShare = await ComponentPrivateShares.findOne({
            access_token: accessToken,
            access_type: shareType.BY_LINK,
            shared_by: { $ne: userId },
            is_active: true,
            is_master: true // Only find master records
        });

        if (!masterShare) {
            return {
                success: false,
                isValid: true,
                message: 'This is an invalid access or is an email invite'
            };
        }

        // Check if user is authenticated
        if (!userId) {
            return {
                success: false,
                isValid: true,
                requiresAuth: true,
                message: 'Authentication required to access this project'
            };
        }

        // Check if the user has already access to the shared component by invite
        const hasAccessByInvite = await ComponentPrivateShares.hasAccess(
            masterShare.component_id,
            userId,
            userEmail
        );

        if (hasAccessByInvite.hasAccess && hasAccessByInvite.accessType === shareType.BY_INVITE) {
            return {
                isValid: false,
                message: 'You already have access to this project via an email invite',
                errorType: 'duplicate_access',
                statusCode: constants.accepted_code
            };
        }

        // Check if user already has an access record for this link
        let accessRecord = await ComponentPrivateShares.findOne({
            master_share_id: masterShare._id,
            shared_with_user: userId,
            shared_with_email: userEmail,
            is_active: true,
            is_master: false
        });

        // Check if link is active (not disabled)
        if (masterShare.status !== privateShareStatus.ACTIVE && !accessRecord) {
            return {
                isValid: false,
                message: 'This share link has been disabled',
                errorType: 'disabled_link',
                statusCode: constants.accepted_code
            };
        }

        // Get component details
        const component = await Components.findById(masterShare.component_id)
            .select('title slug image_url thumbnail_url short_description is_paid component_state created_by_user')
            .lean();

        if (!component) {
            return {
                isValid: false,
                message: 'Project not found',
                errorType: 'not_found',
                statusCode: constants.accepted_code
            };
        }

        // Check if the shared component is still private (security check)
        if (component && component.component_state !== componentState.PRIVATE) {
            return {
                isValid: false,
                message: 'This project is no longer private and can be accessed directly',
                errorType: 'not_private',
                statusCode: constants.accepted_code
            };
        }

        if (accessRecord) {
            // Update existing access record
            accessRecord = await ComponentPrivateShares.findByIdAndUpdate(
                accessRecord._id,
                {
                    $inc: { access_count: 1 },
                    accessed_at: new Date(),
                    user_agent: userAgent,
                    ip_address: ipAddress
                },
                { new: true }
            );
        } else if (!masterShare.expires_at || new Date() < new Date(masterShare.expires_at)) {
            // Create new access record
            accessRecord = await ComponentPrivateShares.create({
                component_id: masterShare.component_id,
                shared_by: masterShare.shared_by,
                access_type: shareType.BY_LINK,
                access_token: accessToken, // Same token as master
                shared_with_user: userId,
                shared_with_email: userEmail,
                status: privateShareStatus.ACCEPTED,
                expires_at: masterShare.expires_at,
                access_duration: masterShare.access_duration,
                duration_days: masterShare.duration_days,
                access_controls: masterShare.access_controls,
                link_name: masterShare.link_name,
                access_count: 1,
                accessed_at: new Date(),
                accepted_at: new Date(),
                is_master: false, // This is an access record
                master_share_id: masterShare._id, // Reference to master
                user_agent: userAgent,
                ip_address: ipAddress
            });
        } else {
            return {
                isValid: false,
                message: 'This share link has expired',
                errorType: 'expired_link',
                statusCode: constants.accepted_code
            };
        }

        // Update total access count on master record
        await ComponentPrivateShares.findByIdAndUpdate(masterShare._id, {
            $inc: { access_count: 1 }
        }, { new: true });


        return {
            success: true,
            message: 'Access granted successfully',
            component: component,
            share: {
                _id: masterShare._id,
                link_name: masterShare.link_name,
                access_type: masterShare.access_type,
                expires_at: masterShare.expires_at,
                shared_by: masterShare.shared_by
            },
            access_controls: masterShare.access_controls,
            accessRecord: {
                _id: accessRecord._id,
                access_count: accessRecord.access_count,
                accessed_at: accessRecord.accessed_at
            }
        };
    } catch (error) {
        logger.error(`Error in accessShareableLink: ${error.message}`);
        throw error;
    }
}

/**
 * Share a component privately with multiple emails (by invite)
 */
async function shareComponentPrivately(
    componentId,
    sharedBy,
    sharerEmail,
    emails,
    personalMessage = '',
    accessDuration = 'undefined',
    durationDays = null,
    accessControls = [],
    frontendUrl = null
) {
    try {
        // Validate component exists, user owns it, and it's private
        const component = await Components.findOne({
            _id: componentId,
            created_by_user: sharedBy,
            component_state: componentState.PRIVATE
        }).lean();

        if (!component) {
            throw new Error(
                'Project not found, you do not have permission to share it, or project must be private to share privately'
            );
        }

        // Validate emails
        const validEmails = [];
        const invalidEmails = [];
        const duplicateEmails = [];
        const sharerEmailLower = sharerEmail.toLowerCase();

        for (const email of emails) {
            const trimmedEmail = email.trim().toLowerCase();

            if (trimmedEmail === sharerEmailLower) {
                if (emails.length === 1) {
                    throw new Error('You cannot share with your own email address');
                }

                invalidEmails.push({
                    oid: 1,
                    email: email,
                    reason: 'Cannot share with self'
                });
                continue;
            }

            if (!isValidEmail(trimmedEmail)) {
                invalidEmails.push({
                    oid: 2,
                    email: email,
                    reason: 'Invalid email address'
                });
                continue;
            }

            // Check if already shared with this email
            const existingShare = await ComponentPrivateShares.findOne({
                component_id: componentId,
                shared_with_email: trimmedEmail,
                status: {
                    $in: [privateShareStatus.PENDING, privateShareStatus.ACCEPTED]
                },
                $or: [
                    { expires_at: { $gt: new Date() } },
                    { expires_at: null }
                ],
                is_active: true
            });

            if (existingShare) {
                duplicateEmails.push(email);
                continue;
            }

            validEmails.push(trimmedEmail);
        }

        if (invalidEmails.length > 0) {
            invalidEmails.forEach((emailObj) => {
                if (emailObj.oid === 1) {
                    throw new Error('You cannot share with your own email address, Please remove it from the list.');
                } else if (emailObj.oid === 2) {
                    throw new Error('One or more email addresses are invalid, Please check and try again.');
                }
            });
        }

        if (validEmails.length === 0) {
            if (duplicateEmails.length > 0) {
                const isSingle = duplicateEmails.length === 1;
                const emailList = duplicateEmails.join(', ');

                throw new Error(
                    `This project is already shared with ${isSingle ? 'this email' : 'these emails'}: ${emailList}`
                );
            }

            throw new Error('No valid email addresses provided');
        }

        // Get sharer details for email
        const sharer = await Users.findById(sharedBy)
            .select('first_name last_name username email')
            .lean();

        const shares = [];
        const expiresAt =
            accessDuration === ACCESS_DURATION.DAYS && durationDays
                ? moment.utc().add(durationDays, ACCESS_DURATION.DAYS).set({
                    hour: 23,
                    minute: 59,
                    second: 59,
                    millisecond: 0
                }).toDate() : null;

        // Create shares for valid emails
        for (const email of validEmails) {
            const accessToken = generateAccessToken();

            const shareData = {
                component_id: componentId,
                shared_by: sharedBy,
                access_type: shareType.BY_INVITE,
                shared_with_email: email,
                access_token: accessToken,
                expires_at: expiresAt,
                access_duration: accessDuration,
                duration_days: durationDays,
                access_controls: accessControls,
                personal_message: personalMessage,
                status: privateShareStatus.PENDING // All shares start as pending until validated
            };

            const share = await ComponentPrivateShares.create(shareData);

            if (share.status === privateShareStatus.ACCEPTED) {
                // Force reset to pending - user must click the link to accept
                await ComponentPrivateShares.findByIdAndUpdate(share._id, {
                    status: privateShareStatus.PENDING,
                    accepted_at: null,
                    updated_at: new Date()
                });
                logger.info(`Fixed share ${share._id} - reset status from auto-accepted to pending`);

                // Update the share object for consistency
                share.status = privateShareStatus.PENDING;
                share.accepted_at = null;
            }

            shares.push(share);

            // Send invitation email
            try {
                await sendPrivateShareInvitationEmail(
                    email,
                    sharer,
                    component,
                    accessToken,
                    personalMessage,
                    expiresAt,
                    frontendUrl
                );
            } catch (emailError) {
                logger.error(
                    `Failed to send invitation email to ${email}: ${emailError.message}`
                );
                // Continue with other emails even if one fails
            }
        }

        return {
            success: true,
            shares,
            summary: {
                total: emails.length,
                successful: validEmails.length,
                invalid: invalidEmails,
                duplicates: duplicateEmails
            }
        };
    } catch (error) {
        logger.error(`Error in shareComponentPrivately: ${error.message}`);
        throw error;
    }
}

/**
 * Unified validation for both shareable links and email invites
 * Always returns auth flags - frontend handles authentication flow
 */
async function acceptPrivateShare(
    accessToken,
    userId = null,
    userEmail = null,
    userAgent = null,
    ipAddress = null
) {
    try {
        // First, try to validate as shareable link (multi-use)
        const shareableLinkResult = await accessShareableLink(accessToken, userId, userEmail, userAgent, ipAddress);

        if (shareableLinkResult.success) {
            return {
                success: true,
                component: shareableLinkResult.component,
                share: shareableLinkResult.share,
                accessType: shareType.BY_LINK,
                requiresSignup: false,
                requiresLogin: false,
                authRequired: false,
                accessControls: shareableLinkResult.access_controls || [],
                accessRecord: shareableLinkResult.accessRecord
            };
        }

        if (!shareableLinkResult.isValid) {
            return {
                isValid: false,
                message: shareableLinkResult.message,
                errorType: shareableLinkResult.errorType,
                statusCode: shareableLinkResult.statusCode
            };
        }

        // If shareable link failed and user needs auth, determine signup vs login
        if (shareableLinkResult.requiresAuth) {
            // Check if user exists in the (for shareable links, we need to check by session email if available)
            let requiresSignup = true;
            let requiresLogin = false;

            if (userEmail) {
                // Check if user with this email exists
                const existingUser = await Users.findOne({
                    email: userEmail,
                    is_verified: true,
                    is_active: true
                }).lean();

                if (existingUser) {
                    requiresSignup = false;
                    requiresLogin = true;
                }
            }

            return {
                success: true,
                component: shareableLinkResult.component,
                shareType: 'shareable_link',
                accessType: shareType.BY_LINK,
                requiresSignup: requiresSignup,
                requiresLogin: requiresLogin,
                authRequired: true,
                accessControls: []
            };
        }

        // If not a shareable link, try email invite validation
        const validation = await ComponentPrivateShares.validateTokenAccess(
            accessToken,
            userId,
            userEmail
        );

        if (!validation.isValid) {
            return {
                success: false,
                message: validation.error,
                errorType: validation.errorType,
                statusCode: validation.statusCode
            };
        }

        const { share, accessType, requiresSignup, requiresLogin } = validation;

        // Get component details
        const component = await Components.findById(share.component_id)
            .select('title slug image_url thumbnail_url short_description is_paid component_state created_by_user')
            .lean();

        // Check if component still exists and is accessible
        if (!component) {
            return {
                success: false,
                message: 'The shared project is no longer available',
                errorType: 'not_found',
                statusCode: constants.accepted_code
            };
        }

        // Check if component is still private (security check)
        if (component.component_state !== componentState.PRIVATE) {
            return {
                success: false,
                message: 'This project is no longer private and can be accessed directly',
                errorType: 'not_private',
                statusCode: constants.accepted_code
            };
        }

        // If authentication is required, return auth flags
        if (requiresSignup || requiresLogin) {
            return {
                success: true,
                component: component,
                sharedBy: share.shared_by,
                accessType: accessType,
                requiresSignup: requiresSignup,
                requiresLogin: requiresLogin,
                authRequired: true,
                accessControls: share.access_controls || []
            };
        }

        // User is authenticated - process email invite
        const updateData = {
            accessed_at: new Date()
        };

        //Check if is the first time the user is accessing the share
        const isFirstAcceptance = !share.accepted_at;

        // For email invites, if user is authenticated and share is not already assigned to a user
        if (userId && !share.shared_with_user) {
            updateData.shared_with_user = userId;
            updateData.shared_with_email = userEmail;
            updateData.status = privateShareStatus.ACCEPTED;
            updateData.$inc = { access_count: 1 };
            updateData.user_agent = userAgent;
            updateData.ip_address = ipAddress;
            updateData.updated_at = new Date();
            updateData.accepted_at = new Date();
        }

        // If share is already assigned to this user, just update access info
        if (
            userId &&
            share.shared_with_user &&
            share.shared_with_user.toString() === userId.toString()
        ) {
            updateData.status = privateShareStatus.ACCEPTED;
            updateData.$inc = { access_count: 1 };
            updateData.user_agent = userAgent;
            updateData.ip_address = ipAddress;
            updateData.updated_at = new Date();
            if (!share.accepted_at) {
                updateData.accepted_at = new Date();
            }
        }

        const updatedShare = await ComponentPrivateShares.findOneAndUpdate({ _id: share._id }, updateData, { new: true, lean: true });

        // Send notification to share creator for email invites when first accepted
        if (accessType === shareType.BY_INVITE && userId && isFirstAcceptance) {
            try {
                const [sharedBy, sharedWith] = await Promise.all([
                    Users.findById(share.shared_by).select('email first_name').lean(),
                    Users.findById(userId).select('first_name username').lean()
                ]);

                if (sharedBy && sharedWith) {
                    await sendPrivateShareAcceptedNotification(
                        sharedBy.email,
                        sharedWith.first_name || sharedWith.username,
                        component.title,
                        component.component_type
                    );
                }
            } catch (notificationError) {
                logger.error(
                    `Failed to send share accepted notification: ${notificationError.message}`
                );
                // Don't fail the main operation if notification fails
            }
        }

        return {
            success: true,
            component: component,
            share: updatedShare,
            sharedBy: updatedShare.shared_by,
            accessType: accessType,
            requiresSignup: false,
            requiresLogin: false,
            authRequired: false,
            accessControls: updatedShare.access_controls || []
        };
    } catch (error) {
        logger.error(`Error in acceptPrivateShare: ${error.message}`);
        throw error;
    }
}

/**
 * Check if user has access to a private component
 */
async function checkPrivateAccess(componentId, userId, userEmail) {
    try {
        if (!userId && !userEmail) {
            return { hasAccess: false, accessType: null };
        }

        // Check if user owns the component
        if (userId) {
            const isOwner = await Components.exists({
                _id: componentId,
                created_by_user: userId
            });

            if (isOwner) {
                return { hasAccess: true, accessType: ACCESS_TYPE.OWNER };
            }
        }

        // Check if component is public
        const component = await Components.findById(componentId)
            .select('component_state is_paid')
            .lean();
        if (!component) {
            return { hasAccess: false, accessType: null };
        }

        if (component.component_state === componentState.PUBLISHED) {
            return { hasAccess: true, accessType: ACCESS_TYPE.PUBLIC };
        }

        // Check private share access
        const hasPrivateAccess = await ComponentPrivateShares.hasAccess(
            componentId,
            userId,
            userEmail
        );
        if (hasPrivateAccess.hasAccess) {
            // If component is paid, check if user has unlocked it
            if (component.is_paid && userId) {
                const unlockHistory = await ComponentUnlockHistory.findOne({
                    component_id: componentId,
                    unlock_by: userId,
                    is_active: true
                }).lean();

                if (unlockHistory) {
                    return {
                        hasAccess: true,
                        accessType: ACCESS_TYPE.UNLOCKED,
                        requiresPayment: false
                    };
                } else {
                    return {
                        hasAccess: true,
                        accessType: ACCESS_TYPE.PRIVATE_SHARE,
                        requiresPayment: true
                    };
                }
            }
            return {
                hasAccess: true,
                accessType: ACCESS_TYPE.PRIVATE_SHARE,
                requiresPayment: false
            };
        }

        return { hasAccess: false, accessType: null };
    } catch (error) {
        logger.error(`Error in checkPrivateAccess: ${error.message}`);
        return { hasAccess: false, accessType: null };
    }
}

/**
 * Get components shared by a user with pagination
 */
async function getMyPrivateShares(userId, options = {}) {
    try {
        const { status = null, searchText = null, sort_by = null } = options;
        const limit = parseInt(options.limit) || 12;
        const skip = parseInt(options.skip) || 0;

        const baseConditions = {
            shared_by: new mongoose.Types.ObjectId(userId),
            is_master: false
        };

        if (status) {
            baseConditions.status = status;
        }

        const recordsTotal = await ComponentPrivateShares.countDocuments(baseConditions);

        let sort = { created_at: -1 };

        if (sort_by) {
            if (sort_by === filterTypes.MOST_POPULAR) {
                sort = { 'access_count': -1, created_at: -1 };
            } else if (sort_by === filterTypes.RECENT_ADDITIONS) {
                sort = { created_at: -1 };
            }
        }

        // Build the base pipeline shared by both data and count queries
        const basePipeline = [
            { $match: baseConditions },
            {
                $lookup: {
                    from: 'components',
                    localField: 'component_id',
                    foreignField: '_id',
                    as: 'component_info'
                }
            },
            {
                $addFields: {
                    component_info: { $arrayElemAt: ['$component_info', 0] }
                }
            },
            ...(searchText ? [{
                $match: {
                    $or: [
                        { 'shared_with_email': { $regex: escapeRegex(searchText), $options: 'i' } },
                        { 'component_info.title': { $regex: escapeRegex(searchText), $options: 'i' } },
                        { 'component_info.short_description': { $regex: escapeRegex(searchText), $options: 'i' } }
                    ]
                }
            }] : [])
        ];

        // Create two separate pipelines: one for counting, one for fetching data

        const countPipeline = [
            ...basePipeline,
            { $count: 'count' }
        ];

        const dataPipeline = [
            ...basePipeline,
            {
                $lookup: {
                    from: 'users',
                    localField: 'shared_with_user',
                    foreignField: '_id',
                    pipeline: [
                        {
                            $project: {
                                first_name: 1,
                                last_name: 1,
                                username: 1,
                                avatar: 1
                            }
                        }
                    ],
                    as: 'shared_with_user'
                }
            },
            {
                $lookup: {
                    from: 'users',
                    localField: 'shared_by',
                    foreignField: '_id',
                    pipeline: [
                        {
                            $project: {
                                first_name: 1,
                                last_name: 1,
                                username: 1,
                                avatar: 1
                            }
                        }
                    ],
                    as: 'shared_by_user'
                }
            },
            { $sort: sort },
            { $skip: skip },
            { $limit: limit },
            {
                $project: {
                    _id: 0,
                    share_id: '$_id',
                    access_token: 1,
                    status: 1,
                    expires_at: 1,
                    accessed_at: 1,
                    access_count: 1,
                    created_at: 1,
                    access_type: 1,
                    access_controls: 1,
                    link_name: 1,
                    shared_with_email: 1,
                    // Component specific fields
                    component_id: '$component_info._id',
                    title: '$component_info.title',
                    slug: '$component_info.slug',
                    image_url: '$component_info.image_url',
                    thumbnail_url: '$component_info.thumbnail_url',
                    short_description: '$component_info.short_description',
                    bookmarks: '$component_info.bookmarks',
                    likes: '$component_info.likes',
                    views: '$component_info.views',
                    created_by_user: '$component_info.created_by_user',
                    component_state: '$component_info.component_state',
                    component_type: '$component_info.component_type',
                    is_paid: '$component_info.is_paid',
                    // Shared with User
                    shared_with_user: { $arrayElemAt: ['$shared_with_user', 0] },
                    // Shared by User
                    shared_by_user: { $arrayElemAt: ['$shared_by_user', 0] }
                }
            }
        ];

        // Execute queries
        const listPromise = ComponentPrivateShares.aggregate(dataPipeline);
        let recordsFilteredPromise;

        if (searchText) {
            // Only run the count query if there is a search
            recordsFilteredPromise = ComponentPrivateShares.aggregate(countPipeline);
        } else {
            // Otherwise, the filtered count is the same as the total
            recordsFilteredPromise = Promise.resolve(null);
        }

        // Await both promises concurrently
        const [list, countResult] = await Promise.all([listPromise, recordsFilteredPromise]);

        // Determine the final filtered count
        let recordsFiltered;
        if (searchText) {
            recordsFiltered = countResult.length > 0 ? countResult[0].count : 0;
        } else {
            recordsFiltered = recordsTotal;
        }

        return {
            recordsTotal,
            recordsFiltered,
            list
        };
    } catch (error) {
        logger.error(`Error in getMyPrivateShares: ${error.message}`, {
            error: error.stack,
            userId,
            options
        });
        throw error;
    }
}

/**
 * Get components shared with a user with pagination
 */
async function getComponentsSharedWithMe(userId, options = {}) {
    try {
        const {
            searchText = null
        } = options;
        const limit = parseInt(options.limit) || 12;
        const skip = parseInt(options.skip) || 0;

        // Build match conditions - ONLY show shares where this user is explicitly the recipient by user ID
        const conditions = {
            // Must be specifically shared with this user by ID only
            shared_with_user: new mongoose.Types.ObjectId(userId),
            // Additional security filters
            is_active: true,
            status: privateShareStatus.ACCEPTED
        };

        const componentIds = (
            await ComponentPrivateShares.distinct('component_id', conditions)
        ).map((id) => new mongoose.Types.ObjectId(id));

        const componentFilters = {
            _id: { $in: componentIds }
        };

        const recordsTotal = await Components.countDocuments(componentFilters);

        // Apply search filter to componentFilters if provided
        if (searchText) {
            componentFilters.$or = [
                {
                    title: {
                        $regex: escapeRegex(searchText),
                        $options: 'i'
                    }
                },
                {
                    short_description: {
                        $regex: escapeRegex(searchText),
                        $options: 'i'
                    }
                }
            ];
        }

        const recordsFiltered = await Components.countDocuments(componentFilters);

        const pipeline = [
            { $match: componentFilters },
            // Lookup for private share info - get the specific share for this user
            {
                $lookup: {
                    from: 'component_private_shares',
                    let: { componentId: '$_id' },
                    pipeline: [
                        {
                            $match: {
                                $expr: {
                                    $and: [
                                        { $eq: ['$component_id', '$$componentId'] },
                                        { $eq: ['$shared_with_user', new mongoose.Types.ObjectId(userId)] },
                                        { $eq: ['$is_active', true] },
                                        { $eq: ['$status', privateShareStatus.ACCEPTED] }
                                    ]
                                }
                            }
                        },
                        { $limit: 1 }
                    ],
                    as: 'share_info'
                }
            },
            // Lookup user who shared this component
            {
                $lookup: {
                    from: 'users',
                    let: { sharedBy: { $arrayElemAt: ['$share_info.shared_by', 0] } },
                    pipeline: [
                        {
                            $match: {
                                $expr: { $eq: ['$_id', '$$sharedBy'] }
                            }
                        },
                        {
                            $project: {
                                first_name: 1,
                                last_name: 1,
                                username: 1,
                                avatar: 1
                            }
                        }
                    ],
                    as: 'shared_by_user'
                }
            },
            // Lookup unlock history for this user
            {
                $lookup: {
                    from: 'component_unlock_histories',
                    let: { componentId: '$_id' },
                    pipeline: [
                        {
                            $match: {
                                $expr: {
                                    $and: [
                                        { $eq: ['$component_id', '$$componentId'] },
                                        {
                                            $eq: ['$unlock_by', new mongoose.Types.ObjectId(userId)]
                                        },
                                        { $eq: ['$is_active', true] }
                                    ]
                                }
                            }
                        },
                        { $limit: 1 }
                    ],
                    as: 'unlock_history'
                }
            },
            {
                $addFields: {
                    share_info: { $arrayElemAt: ['$share_info', 0] },
                    shared_by_user: { $arrayElemAt: ['$shared_by_user', 0] },
                    is_unlocked: { $gt: [{ $size: '$unlock_history' }, 0] }
                }
            },
            { $sort: { 'share_info.created_at': -1 } },
            { $skip: skip },
            { $limit: limit },
            // Final projection to match expected response format
            {
                $project: {
                    // Component fields
                    _id: 1,
                    title: 1,
                    slug: 1,
                    image_url: 1,
                    thumbnail_url: 1,
                    short_description: 1,
                    views: 1,
                    likes: 1,
                    bookmarks: 1,
                    created_at: 1,
                    is_paid: 1,
                    purchase_price: 1,
                    component_state: 1,
                    component_type: 1,
                    created_by_user: 1,
                    share_info: 1,
                    shared_by_user: 1,
                    is_unlocked: 1
                }
            }
        ];

        const list = await Components.aggregate(pipeline);

        return {
            recordsTotal,
            recordsFiltered,
            list
        };
    } catch (error) {
        logger.error(`Error in getComponentsSharedWithMe: ${error.message}`, {
            error: error.stack,
            userId,
            options
        });
        throw error;
    }
}

/**
 * Get statistics for components shared with user (Free/Unlocked counts)
 */
async function getSharedWithMeStatistics(userId) {
    try {
        const shareConditions = {
            // Must be specifically shared with this user by ID only
            shared_with_user: new mongoose.Types.ObjectId(userId),
            // Additional security filters
            is_active: true,
            status: privateShareStatus.ACCEPTED
        };

        const componentIds = (
            await ComponentPrivateShares.distinct('component_id', shareConditions)
        ).map((id) => new mongoose.Types.ObjectId(id));

        logger.info(
            `Found ${componentIds.length} shared components for statistics for user ${userId}`
        );

        if (componentIds.length === 0) {
            return {
                total: 0,
                free: 0,
                unlocked: 0,
                locked: 0
            };
        }

        const pipeline = [
            { $match: { _id: { $in: componentIds } } },
            // Lookup unlock history for this user
            {
                $lookup: {
                    from: 'component_unlock_histories',
                    let: { componentId: '$_id' },
                    pipeline: [
                        {
                            $match: {
                                $expr: {
                                    $and: [
                                        { $eq: ['$component_id', '$$componentId'] },
                                        {
                                            $eq: ['$unlock_by', new mongoose.Types.ObjectId(userId)]
                                        },
                                        { $eq: ['$is_active', true] }
                                    ]
                                }
                            }
                        },
                        { $limit: 1 }
                    ],
                    as: 'unlock_history'
                }
            },
            // Add computed payment status
            {
                $addFields: {
                    payment_status: {
                        $switch: {
                            branches: [
                                {
                                    case: { $eq: ['$is_paid', false] },
                                    then: 'free'
                                },
                                {
                                    case: { $gt: [{ $size: '$unlock_history' }, 0] },
                                    then: 'unlocked'
                                }
                            ],
                            default: 'locked'
                        }
                    }
                }
            },
            // Group by payment status to get counts
            {
                $group: {
                    _id: '$payment_status',
                    count: { $sum: 1 }
                }
            }
        ];

        const results = await Components.aggregate(pipeline);

        // Format results
        const statistics = {
            total: 0,
            free: 0,
            unlocked: 0,
            locked: 0
        };

        results.forEach((result) => {
            statistics[result._id] = result.count;
            statistics.total += result.count;
        });

        return statistics;
    } catch (error) {
        logger.error(`Error in getSharedWithMeStatistics: ${error.message}`);
        throw error;
    }
}

/**
 * Revoke a private share
 */
async function revokePrivateShare(shareId, userId) {
    try {
        const share = await ComponentPrivateShares.findOne({
            _id: shareId,
            shared_by: userId,
            status: privateShareStatus.ACCEPTED,
            is_active: true
        });

        if (!share) {
            throw new Error(
                'Share not found or you do not have permission to revoke it'
            );
        }

        await ComponentPrivateShares.updateOne(
            { _id: shareId },
            {
                status: privateShareStatus.REVOKED,
                revoked_at: new Date(),
                revoked_by: userId
            }
        );

        return { success: true, message: 'Share revoked successfully' };
    } catch (error) {
        logger.error(`Error in revokePrivateShare: ${error.message}`);
        throw error;
    }
}

/**
 * Link pending EMAIL INVITES when user signs up
 *
 * Purpose: When a user signs up, link any email invites that were sent to their email
 * before they had an account. This is ONLY needed for email invites, not shareable links.
 *
 * Why needed for email invites:
 * - Email invites are created with: shared_with_email: "<EMAIL>", shared_with_user: null
 * - When user signs up, we need to set: shared_with_user: newUserId
 * - Without this, validate-token API would fail because user ID doesn't match
 *
 * Why NOT needed for shareable links:
 * - Shareable links create child access records on-demand via accessShareableLink
 * - No pre-linking required - handled during validate-token API call
 */
async function linkPendingSharesOnSignup(userId, email) {
    try {
        // Only handle email-based invites - shareable links don't need pre-linking
        const emailInviteQuery = {
            shared_with_email: email,
            shared_with_user: null,
            access_type: shareType.BY_INVITE,
            status: privateShareStatus.PENDING,
            is_active: true
        };

        const pendingEmailInvites = await ComponentPrivateShares.find(emailInviteQuery).lean();

        if (pendingEmailInvites.length === 0) {
            logger.info(`No pending email invites found for user ${userId} (${email})`);
            return 0;
        }

        logger.info(`Found ${pendingEmailInvites.length} pending email invites for user ${userId} (${email})`);

        const emailInviteIds = pendingEmailInvites.map((share) => share._id);
        const now = new Date();

        // Update email invites using atomic operations
        const emailUpdatePromises = emailInviteIds.map((shareId) =>
            ComponentPrivateShares.findOneAndUpdate(
                {
                    _id: shareId,
                    status: privateShareStatus.PENDING,
                    is_active: true,
                    shared_with_user: null
                },
                {
                    $set: {
                        shared_with_user: userId,
                        shared_with_email: email,
                        status: privateShareStatus.ACCEPTED,
                        accepted_at: now,
                        updated_at: now
                    }
                },
                { new: true, lean: true }
            )
        );

        const emailUpdateResults = await Promise.allSettled(emailUpdatePromises);
        const successfulEmailUpdates = emailUpdateResults
            .filter((result) => result.status === 'fulfilled' && result.value !== null)
            .map((result) => result.value);

        const linkedCount = successfulEmailUpdates.length;

        if (linkedCount > 0) {
            await sendShareAcceptedNotifications(successfulEmailUpdates, userId);
            logger.info(`Successfully linked ${linkedCount} email invites for user ${userId}`);
        }

        return linkedCount;
    } catch (error) {
        logger.error(
            `Error linking pending shares for user ${userId} (${email}): ${error.message}`,
            {
                error: error.stack,
                userId,
                email
            }
        );
        throw error; // Re-throw to be handled by the caller
    }
}

//Send notifications for accepted shares (separated for better error handling)
async function sendShareAcceptedNotifications(linkedShares, userId) {
    if (!linkedShares?.length || !userId) {
        logger.warn('Invalid parameters for sendShareAcceptedNotifications', {
            linkedSharesCount: linkedShares?.length,
            userId
        });
        return;
    }

    try {
        logger.info(`Sending share accepted notifications for user ${userId} (${linkedShares.length} shares)`);

        // Get component and user details
        const componentIds = [...new Set(linkedShares.map((share) => share.component_id))];
        const sharerIds = [...new Set(linkedShares.map((share) => share.shared_by))];

        const [components, sharedWithUser, sharers] = await Promise.all([
            Components.find({ _id: { $in: componentIds } })
                .select('name title created_by_user component_type')
                .lean(),
            Users.findById(userId).select('first_name last_name username email').lean(),
            Users.find({ _id: { $in: sharerIds } }).select('email first_name').lean()
        ]);

        if (!sharedWithUser) {
            logger.warn(`User ${userId} not found for notification`);
            return;
        }

        // Create maps for quick lookup
        const componentMap = components.reduce((acc, comp) => {
            acc[comp._id.toString()] = comp;
            return acc;
        }, {});

        const sharerMap = sharers.reduce((acc, sharer) => {
            acc[sharer._id.toString()] = sharer;
            return acc;
        }, {});

        // Group shares by sharer to batch notifications
        const notificationsBySharer = linkedShares.reduce((acc, share) => {
            const sharerId = share.shared_by.toString();
            if (!acc[sharerId]) {
                acc[sharerId] = [];
            }
            acc[sharerId].push(share);
            return acc;
        }, {});

        // Send batched notifications
        const notificationPromises = Object.entries(notificationsBySharer).map(
            async ([sharerId, shares]) => {
                const sharer = sharerMap[sharerId];
                if (!sharer?.email) {
                    logger.warn(`No email found for sharer ${sharerId}`);
                    return;
                }

                // Get component names for this notification
                const componentNames = shares
                    .map((share) => {
                        const comp = componentMap[share.component_id.toString()];
                        return comp?.name || comp?.title || 'a component';
                    })
                    .filter(Boolean);

                if (componentNames.length === 0) {
                    logger.warn(`No valid components found for notification to ${sharer.email}`);
                    return;
                }

                const componentTypes = shares
                    .map((share) => {
                        const comp = componentMap[share.component_id.toString()];
                        return comp?.component_type || 'component';
                    })
                    .filter(Boolean);

                try {
                    await sendPrivateShareAcceptedNotification(
                        sharer.email,
                        sharedWithUser.first_name || sharedWithUser.username || 'Someone',
                        componentNames.join(', '),
                        componentTypes.join(', ')
                    );
                    logger.info(`Sent share accepted notification to ${sharer.email}`);
                } catch (error) {
                    logger.error(
                        `Failed to send notification to ${sharer.email}: ${error.message}`,
                        { error }
                    );
                    // Don't throw - continue with other notifications
                }
            }
        );

        await Promise.allSettled(notificationPromises);
        logger.info(`Completed sending ${notificationPromises.length} share accepted notifications`);

    } catch (error) {
        logger.error(`Error in sendShareAcceptedNotifications: ${error.message}`, {
            error,
            userId,
            shareCount: linkedShares?.length
        });
        // Don't throw - notifications are non-critical
    }
}

/**
 * Cleanup expired shares (to be called by cron job)
 */
async function cleanupExpiredShares() {
    try {
        const expiredCount = await ComponentPrivateShares.cleanupExpiredShares();
        logger.info(`Marked ${expiredCount} shares as expired`);
        return expiredCount;
    } catch (error) {
        logger.error(`Error cleaning up expired shares: ${error.message}`);
        return 0;
    }
}

/**
 * Get user's generated shareable links
 */
async function getMyShareableLinks(userId, options = {}) {
    try {
        const { searchText = null, sort_by = null } = options;
        const limit = parseInt(options.limit) || 12;
        const skip = parseInt(options.skip) || 0;

        const baseConditions = {
            shared_by: new mongoose.Types.ObjectId(userId),
            access_type: shareType.BY_LINK,
            is_active: true,
            status: privateShareStatus.ACTIVE,
            is_master: true
        };

        const recordsTotal = await ComponentPrivateShares.countDocuments(baseConditions);

        let sort = { created_at: -1 };
        if (sort_by === filterTypes.MOST_POPULAR) {
            sort = { 'access_count': -1, created_at: -1 };
        }

        // Build the base pipeline shared by both data and count queries
        const basePipeline = [
            { $match: baseConditions },
            {
                $lookup: {
                    from: 'components',
                    localField: 'component_id',
                    foreignField: '_id',
                    as: 'component_info'
                }
            },
            {
                $addFields: {
                    component_info: { $arrayElemAt: ['$component_info', 0] }
                }
            },
            ...(searchText ? [{
                $match: {
                    $or: [
                        { 'link_name': { $regex: escapeRegex(searchText), $options: 'i' } },
                        { 'component_info.title': { $regex: escapeRegex(searchText), $options: 'i' } },
                        { 'component_info.short_description': { $regex: escapeRegex(searchText), $options: 'i' } }
                    ]
                }
            }] : [])
        ];

        // Create two separate pipelines: one for counting, one for fetching data

        const countPipeline = [
            ...basePipeline,
            { $count: 'count' }
        ];

        const dataPipeline = [
            ...basePipeline,
            {
                $lookup: {
                    from: 'users',
                    localField: 'shared_by',
                    foreignField: '_id',
                    pipeline: [
                        {
                            $project: {
                                first_name: 1,
                                last_name: 1,
                                username: 1,
                                avatar: 1
                            }
                        }
                    ],
                    as: 'shared_by_user'
                }
            },
            {
                $lookup: {
                    from: 'component_private_shares',
                    localField: '_id',
                    foreignField: 'master_share_id',
                    as: 'access_records'
                }
            },
            {
                $addFields: {
                    unique_users_count: { $size: '$access_records' },
                    recent_accessors: { $slice: [{ $sortArray: { input: '$access_records', sortBy: { accessed_at: -1 } } }, 5] },
                    shared_by_user: { $arrayElemAt: ['$shared_by_user', 0] }
                }
            },
            { $sort: sort },
            { $skip: skip },
            { $limit: limit },
            {
                $project: {
                    _id: 0,
                    share_id: '$_id',
                    link_name: 1,
                    access_token: 1,
                    status: 1,
                    expires_at: 1,
                    access_duration: 1,
                    duration_days: 1,
                    access_controls: 1,
                    access_count: 1,
                    share_created_at: '$created_at',
                    unique_users_count: 1,
                    recent_accessors: {
                        $map: {
                            input: '$recent_accessors',
                            as: 'accessor',
                            in: {
                                accessed_by: '$$accessor.shared_with_user',
                                accessed_at: '$$accessor.accessed_at',
                                access_count: '$$accessor.access_count'
                            }
                        }
                    },
                    component_id: '$component_info._id',
                    title: '$component_info.title',
                    slug: '$component_info.slug',
                    image_url: '$component_info.image_url',
                    thumbnail_url: '$component_info.thumbnail_url',
                    short_description: '$component_info.short_description',
                    is_paid: '$component_info.is_paid',
                    component_type: '$component_info.component_type',
                    component_state: '$component_info.component_state',
                    shared_by_user: 1
                }
            }
        ];

        // Execute queries
        const listPromise = ComponentPrivateShares.aggregate(dataPipeline);
        let recordsFilteredPromise;

        if (searchText) {
            // Only run the count query if there is a search
            recordsFilteredPromise = ComponentPrivateShares.aggregate(countPipeline);
        } else {
            // Otherwise, the filtered count is the same as the total
            recordsFilteredPromise = Promise.resolve(null);
        }

        // Await both promises concurrently
        const [list, countResult] = await Promise.all([listPromise, recordsFilteredPromise]);

        // Determine the final filtered count
        let recordsFiltered;
        if (searchText) {
            recordsFiltered = countResult.length > 0 ? countResult[0].count : 0;
        } else {
            recordsFiltered = recordsTotal;
        }

        return {
            recordsTotal,
            recordsFiltered,
            list
        };

    } catch (error) {
        logger.error(`Error in getMyShareableLinks: ${error.message}`);
        throw error;
    }
}

/**
 * Get share statistics for a user
 */
async function getShareStatistics(userId) {
    try {
        const pipeline = [
            {
                $match: {
                    shared_by: new mongoose.Types.ObjectId(userId),
                    is_active: true
                }
            },
            {
                $group: {
                    _id: '$status',
                    count: { $sum: 1 }
                }
            }
        ];

        const stats = await ComponentPrivateShares.aggregate(pipeline);

        const result = {
            total: 0,
            pending: 0,
            accepted: 0,
            expired: 0,
            revoked: 0
        };

        stats.forEach((stat) => {
            result[stat._id] = stat.count;
            result.total += stat.count;
        });

        return result;
    } catch (error) {
        logger.error(`Error getting share statistics: ${error.message}`);
        throw error;
    }
}

/**
 * Bulk revoke multiple shares
 */
async function bulkRevokeShares(shareIds, userId) {
    try {
        if (!Array.isArray(shareIds) || shareIds.length === 0) {
            throw new Error('Share IDs array is required');
        }

        if (shareIds.length > 50) {
            throw new Error('Maximum 50 shares can be revoked at once');
        }

        const result = await ComponentPrivateShares.updateMany(
            {
                _id: { $in: shareIds },
                shared_by: userId,
                status: privateShareStatus.ACCEPTED,
                is_active: true
            },
            {
                status: privateShareStatus.REVOKED,
                is_active: false,
                revoked_at: new Date(),
                revoked_by: userId
            }
        );

        return {
            success: true,
            revokedCount: result.modifiedCount,
            message: `${result.modifiedCount} share(s) revoked successfully`
        };
    } catch (error) {
        logger.error(`Error in bulk revoke shares: ${error.message}`);
        throw error;
    }
}

/**
 * Get detailed analytics for a specific share (optimized)
 */
async function getShareAnalytics(shareId, userId) {
    try {
        const pipeline = [
            {
                $match: {
                    _id: new mongoose.Types.ObjectId(shareId),
                    shared_by: new mongoose.Types.ObjectId(userId)
                }
            },
            {
                $lookup: {
                    from: 'components',
                    localField: 'component_id',
                    foreignField: '_id',
                    as: 'component',
                    pipeline: [
                        {
                            $project: {
                                title: 1,
                                slug: 1,
                                component_state: 1
                            }
                        }
                    ]
                }
            },
            {
                $lookup: {
                    from: 'users',
                    localField: 'shared_with_user',
                    foreignField: '_id',
                    as: 'shared_with_user',
                    pipeline: [
                        {
                            $project: {
                                username: 1,
                                first_name: 1,
                                last_name: 1,
                                email: 1
                            }
                        }
                    ]
                }
            },
            {
                $set: {
                    component: { $arrayElemAt: ['$component', 0] },
                    shared_with_user: { $arrayElemAt: ['$shared_with_user', 0] },
                    daysSinceCreated: {
                        $divide: [
                            { $subtract: [new Date(), '$created_at'] },
                            86400000 // milliseconds in a day
                        ]
                    },
                    isExpired: {
                        $cond: {
                            if: '$expires_at',
                            then: { $gt: [new Date(), '$expires_at'] },
                            else: false
                        }
                    },
                    daysUntilExpiry: {
                        $cond: {
                            if: '$expires_at',
                            then: {
                                $divide: [{ $subtract: ['$expires_at', new Date()] }, 86400000]
                            },
                            else: null
                        }
                    },
                    lastAccessedDaysAgo: {
                        $cond: {
                            if: '$accessed_at',
                            then: {
                                $divide: [
                                    { $subtract: [new Date(), '$accessed_at'] },
                                    86400000
                                ]
                            },
                            else: null
                        }
                    }
                }
            },
            {
                $set: {
                    accessRate: {
                        $cond: {
                            if: { $gt: ['$access_count', 0] },
                            then: {
                                $round: [
                                    {
                                        $divide: [
                                            '$access_count',
                                            { $max: ['$daysSinceCreated', 1] }
                                        ]
                                    },
                                    2
                                ]
                            },
                            else: 0
                        }
                    }
                }
            }
        ];

        const results = await ComponentPrivateShares.aggregate(pipeline);
        const share = results[0];

        if (!share) {
            throw new Error(
                'Share not found or you do not have permission to view it'
            );
        }

        return share;
    } catch (error) {
        logger.error(`Error getting share analytics: ${error.message}`);
        throw error;
    }
}

/**
 * Refresh shareable link (generate new token)
 */
async function refreshShareableLink(shareId, userId, frontendUrl = null) {
    try {
        // Find the master share record and verify ownership
        const masterShare = await ComponentPrivateShares.findOne({
            _id: shareId,
            shared_by: userId,
            access_type: shareType.BY_LINK,
            is_active: true,
            is_master: true // Only allow refresh of master records
        });

        if (!masterShare) {
            throw new Error(
                'Shareable link not found or you do not have permission to refresh it'
            );
        }

        // Check if there are any child access records
        const childRecords = await ComponentPrivateShares.find({
            master_share_id: shareId,
            is_master: false,
            is_active: true
        }).select('_id shared_with_user access_count').lean();

        const hasChildRecords = childRecords.length > 0;
        const totalChildAccesses = childRecords.reduce((sum, record) => sum + record.access_count, 0);
        const uniqueUsers = [...new Set(childRecords.map((record) => record.shared_with_user.toString()))];

        // Generate new access token
        const newAccessToken = crypto.randomBytes(32).toString('hex');

        let refreshAction = '';
        const oldTokenInfo = {
            old_token: masterShare.access_token,
            had_child_records: hasChildRecords,
            total_accesses: totalChildAccesses,
            unique_users_count: uniqueUsers.length
        };

        if (hasChildRecords) {
            // Set old master to "disabled" status
            await ComponentPrivateShares.findByIdAndUpdate(shareId, {
                status: privateShareStatus.DISABLED
            });

            // Create completely new master record
            const newMasterShare = await ComponentPrivateShares.create({
                component_id: masterShare.component_id,
                shared_by: masterShare.shared_by,
                access_type: shareType.BY_LINK,
                access_token: newAccessToken,
                expires_at: masterShare.expires_at,
                access_duration: masterShare.access_duration,
                duration_days: masterShare.duration_days,
                access_controls: masterShare.access_controls,
                link_name: masterShare.link_name,
                status: privateShareStatus.ACTIVE, // Multi-use links start as active
                shared_with_user: null,
                shared_with_email: null,
                access_count: 0,
                is_master: true,
                master_share_id: null
            });

            refreshAction = 'refresh_new_master';

            // Generate shareable URL
            let shareableUrl;
            if (frontendUrl) {
                const separator = frontendUrl.includes('?') ? '&' : '?';
                shareableUrl = `${frontendUrl}${separator}token=${newAccessToken}`;
            }

            return {
                success: true,
                action: refreshAction,
                share: newMasterShare,
                shareableUrl: shareableUrl,
                message: 'New shareable link created. Previous link disabled to preserve user access.',
                oldTokenInfo: oldTokenInfo,
                note: 'Previous users retain component access but old link is disabled'
            };

        } else {
            // No child records, safe to update existing master
            const updatedShare = await ComponentPrivateShares.findByIdAndUpdate(
                shareId,
                {
                    access_token: newAccessToken,
                    status: privateShareStatus.ACTIVE, // Keep active status
                    access_count: 0, // Reset access count
                    updated_at: new Date()
                },
                { new: true }
            );

            refreshAction = 'refresh_same_master';

            // Generate shareable URL
            let shareableUrl;
            if (frontendUrl) {
                const separator = frontendUrl.includes('?') ? '&' : '?';
                shareableUrl = `${frontendUrl}${separator}token=${newAccessToken}`;
            }

            return {
                success: true,
                action: refreshAction,
                share: updatedShare,
                shareableUrl: shareableUrl,
                message: 'Shareable link refreshed successfully',
                oldTokenInfo: oldTokenInfo,
                note: 'No users had accessed the previous link'
            };
        }

    } catch (error) {
        logger.error(`Error in refreshShareableLink: ${error.message}`, {
            error: error.stack,
            shareId,
            userId
        });
        throw error;
    }
}

module.exports = {
    // Core sharing functions
    shareComponentPrivately,
    generatePublicShareableLink,
    acceptPrivateShare,
    accessShareableLink,

    // Access control
    checkPrivateAccess,

    // Data retrieval functions
    getMyPrivateShares,
    getMyShareableLinks,
    getComponentsSharedWithMe,
    getSharedWithMeStatistics,

    // Management functions
    revokePrivateShare,
    bulkRevokeShares,
    refreshShareableLink,

    // Statistics and analytics
    getShareStatistics,
    getShareAnalytics,

    // Utility functions
    linkPendingSharesOnSignup,
    cleanupExpiredShares,
    generateAccessToken
};