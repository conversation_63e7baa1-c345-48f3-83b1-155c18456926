// Common Route for each version where we categorize it by modules
const express = require('express');
const app = express();
const { validationErrorHandler } = require('../../middlewares/validations/validationErrorHandler.js');

const component = require('./componentRoute.js').router;
const componentAuthRoute = require('./componentRoute.js').authRouter;
const category = require('./categoryRoute.js');
const blogs = require('./blogRoute.js');
const auth = require('./authRoute.js');
const users = require('./userRoute.js');
const interestedUsers = require('./interestedUserRoute.js');
const staticPages = require('./staticPagesRoute.js');
const settings = require('./settingsRoute.js');
const repository = require('./repositoryRoute.js');
const codeSpace = require('./codeSpaceRoute.js');
const utility = require('./utilityRoute.js');
const publishedCodeSpace = require('./publishedCodeSpaceRoute.js');
const usersPublic = require('./userPublicRoute.js');
const collections = require('./collectionRoute.js');
const healthCheck = require('./healthCheckRoute.js');
const elements = require('./elementRoute.js');
const notifications = require('./notificationRoute.js');
const homepage = require('./homepageRoute.js');
const componentReport = require('./componentReportRoute.js');
const newsletters = require('./newsletterRoute.js');
const collaborations = require('./collaboratorRoute.js');
const webhooks = require('./webhookRoute.js');
const creatorFollow = require('./creatorFollowRoute.js');
const componentPrivateShare = require('./componentPrivateShareRoute.js');
const challenges = require('./challengeRoute.js');

// route grouping without token
app.prefix('/component', function (router) { // Any route
    router.use('/', component);
});

app.prefix('/v1/component', function (router) { // Any route
    router.use('/', componentAuthRoute);
});

app.prefix('/category', function (router) { // Any route
    router.use('/', category);
});

app.prefix('/blogs', function (router) { // Any route
    router.use('/', blogs);
});

app.prefix('/auth', function (router) { // Any route
    router.use('/', auth);
});

app.prefix('/v1/users', function (router) { // Any route
    router.use('/', users);
});

app.prefix('/user-interest', function (router) { // Any route
    router.use('/', interestedUsers);
});

app.prefix('/pages', function (router) { // Any route
    router.use('/', staticPages);
});

app.prefix('/settings', function (router) {
    router.use('/', settings);
});

app.prefix('/v1/repository', function (router) {
    router.use('/', repository);
});

app.prefix('/v1/code-space', function (router) { // Any route
    router.use('/', codeSpace);
});

app.prefix('/v1/utility', function (router) { // Any route
    router.use('/', utility);
});

app.prefix('/v1/published/code-space', function (router) { // Any route
    router.use('/', publishedCodeSpace);
});

app.prefix('/users', function (router) { // Any route
    router.use('/', usersPublic);
});

app.prefix('/v1/collections', function (router) { // Any route
    router.use('/', collections);
});

app.prefix('/health', function (router) {
    router.use('/', healthCheck);
});

app.prefix('/v1/elements', function (router) { // Any route
    router.use('/', elements);
});

app.prefix('/v1/notifications', function (router) { // Any route
    router.use('/', notifications);
});

app.prefix('/homepage', function (router) { // Any route
    router.use('/', homepage);
});

app.prefix('/v1/component-reports', function (router) { // Any route
    router.use('/', componentReport);
});

app.prefix('/newsletters', function (router) { // Any route
    router.use('/', newsletters);
});

app.prefix('/v1/collaboration', function (router) { // Any route
    router.use('/', collaborations);
});

// publicly callable webhooks
app.prefix('/webhooks', function (router) { // Any route
    router.use('/', webhooks);
});

app.prefix('/v1/creators', function (router) { // Any route
    router.use('/', creatorFollow);
});

// Private share routes (public routes without authentication)
app.prefix('/component', function (router) {
    router.use('/', componentPrivateShare.router);
});

// Private share routes (authenticated routes)
app.prefix('/v1/component', function (router) {
    router.use('/', componentPrivateShare.authRouter);
});

app.prefix('/v1/challenges', function (router) { // Any route
    router.use('/', challenges);
});

// validation handler
app.use(validationErrorHandler);
module.exports = app;