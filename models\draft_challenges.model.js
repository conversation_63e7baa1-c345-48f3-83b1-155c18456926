const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const { challengeState, componentType } = require('../config/component.constant');

const draftChallengeSchema = new Schema({
    title: {
        type: String,
        required: true
    },
    slug: {
        type: String,
        required: true
    },
    challenge_type: {
        type: String,
        default: componentType.MANUAL,
        enum: Object.values(componentType)
    },
    challenge_state: {
        type: String,
        default: challengeState.PLACEHOLDER,
        enum: Object.values(challengeState)
    },
    description: {
        type: String
    },
    image_url: {
        type: String
    },
    created_by_user: {
        type: mongoose.Types.ObjectId,
        ref: 'users'
    },
    updated_by_user: {
        type: mongoose.Types.ObjectId,
        ref: 'users'
    },
    start_date: {
        type: Date
    },
    end_date: {
        type: Date
    },
    result_date: {
        type: Date
    },
    reference_id: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'components'
    },
    category_id: {
        type: mongoose.Types.ObjectId,
        ref: 'category'
    },
    identification_tag: [{
        type: String
    }]
}, {
    timestamps: {
        createdAt: 'created_at',
        updatedAt: 'updated_at'
    }
});

const DraftChallenges = mongoose.model('draft_challenges', draftChallengeSchema);

module.exports = {
    DraftChallenges
};