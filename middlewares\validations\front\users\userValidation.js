const { editProfile, createGitlabAccount, sendOTPForSecurityAction, updateUserEmail, generateGitlabAccessToken } = require('../../../../validations/front/users/userValidation');

class UserValidationMiddleware {
    editProfileValidation(req, res, next) {
        const { value, error } = editProfile(req.body);
        if (error) {
            return next(error);
        }
        req.body = value;
        next();
    }

    createGitlabAccountValidation(req, res, next) {
        const { value, error } = createGitlabAccount(req.body);
        if (error) {
            return next(error);
        }
        req.body = value;
        next();
    }

    sendOTPForSecurityActionValidation(req, res, next) {
        const { value, error } = sendOTPForSecurityAction(req.body);
        if (error) {
            return next(error);
        }
        req.body = value;
        next();
    }

    updateUserEmailValidation(req, res, next) {
        const { value, error } = updateUserEmail(req.body);
        if (error) {
            return next(error);
        }
        req.body = value;
        next();
    }

    generateGitlabAccessTokenValidation(req, res, next) {
        const { value, error } = generateGitlabAccessToken(req.body);
        if (error) {
            return next(error);
        }
        req.body = value;
        next();
    }
}
module.exports = new UserValidationMiddleware();