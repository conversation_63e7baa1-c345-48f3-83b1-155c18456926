const { createChallengePlaceholder } = require('../../../../validations/front/challenge/challengeValidation');

class ChallengeValidationMiddleware {
    createChallengePlaceholderValidation(req, res, next) {
        const { value, error } = createChallengePlaceholder(req.body);
        if (error) {
            return next(error);
        }
        req.body = value;
        next();
    }
}
module.exports = new ChallengeValidationMiddleware();