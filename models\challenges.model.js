const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const { challengeState, componentType } = require('../config/component.constant');

const challengeSchema = new Schema({
    title: {
        type: String,
        required: true
    },
    slug: {
        type: String,
        required: true
    },
    challenge_type: {
        type: String,
        default: componentType.MANUAL,
        enum: Object.values(componentType)
    },
    challenge_state: {
        type: String,
        default: challengeState.PUBLISHED,
        enum: Object.values(challengeState)
    },
    description: {
        type: String
    },
    image_url: {
        type: String
    },
    created_by_user: {
        type: mongoose.Types.ObjectId,
        ref: 'users'
    },
    updated_by_user: {
        type: mongoose.Types.ObjectId,
        ref: 'users'
    },
    start_date: {
        type: Date
    },
    end_date: {
        type: Date
    },
    result_date: {
        type: Date
    },
    reference_id: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'components'
    },
    category_id: {
        type: mongoose.Types.ObjectId,
        ref: 'category'
    },
    identification_tag: [{
        type: String
    }],
    challenge_draft_id: {
        type: mongoose.Types.ObjectId,
        ref: 'draft_challenges'
    },
    published_count: {
        type: Number,
        default: 0
    }
}, {
    timestamps: {
        createdAt: 'created_at',
        updatedAt: 'updated_at'
    }
});

const Challenges = mongoose.model('challenges', challengeSchema);

module.exports = {
    Challenges
};