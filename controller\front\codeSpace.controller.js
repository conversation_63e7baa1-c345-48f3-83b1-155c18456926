// Constants declaration
const constants = require('../../config/constants');
const logger = require('../../config/logger');

const { gitlabDefaultBranch, gitlabAccessLevel, projectVisibility, codeSpaceFilterTypes, gitlabProductionBranch, repositoryState, publishState, gitlabImportStatus } = require('../../config/gitlab.constant');
const { componentState, componentType } = require('../../config/component.constant');

// Service declaration
const { ReS, sendError, escapeRegex, generateCodeSpaceSlug } = require('../../services/general.helper');
const { createNewGitlabProject, addMemberToProject, protectBranch, checkProjectNameAvailability, addReadmeFileToRepository, fetchCommitList, findRepositoryFile, fetchRepoContent, fetchProjectLanguages, checkBranchDifferencesForProjects, createProjectFork, checkBranchDifferencesForSyncWithFork, createGitlabBranch, updateProjectDefaultBranch, createGitLabMergeRequestWithFork, updateProjectVisibility, deleteGitlabProject } = require('./../../services/gitlab.helper');

const { prepareReadmeFileContent, publishComponentRepository, cacheRepositoryDataInRedis, isCodespaceDeletable } = require('./../../services/repository.service');
const { createComponentForPublicCodeSpace } = require('./../../services/component.service');
const { fetchAndSyncUserUsedStorage } = require('./../../services/gitlab_webhook.service');

// Models declaration

const GitlabUsers = require('../../models/gitlab_users.model').GitlabUsers;
const GitlabRepository = require('../../models/gitlab_repository.model').GitlabRepository;
const Collaborator = require('../../models/collaborator.model').Collaborator;
const { DraftComponents } = require('../../models/draft_components.model');
const { Components } = require('../../models/component.model');

// NPM declaration
const mongoose = require('mongoose');

async function createNewRepository(req, res) {
    try {
        // Destructure required fields from the request body
        const { project_name, platform_id, description, initialize_with_readme, component_id, state, is_challenge, challenge_meta } = req.body;

        // Generate a URL-friendly slug from the project name
        const projectSlug = generateCodeSpaceSlug(project_name);

        // Fetch GitLab user information based on the admin ID from the session
        const gitlabUser = await GitlabUsers.findOne({ user_id: req.session._id }).lean();

        // If the GitLab user record is not found, return an error response
        if (!gitlabUser) {
            return ReS(res, constants.bad_request_code, 'Oops! Your creator profile does not exist.');
        }

        // Decide project visibility based on provided state
        const visibility = (state === repositoryState.PUBLIC) ? projectVisibility.INTERNAL : projectVisibility.PRIVATE;

        // Create a new GitLab project using the generated slug
        const newProject = await createNewGitlabProject(projectSlug, gitlabProductionBranch, false, visibility);

        // Add the GitLab user as a member of the new project with DEVELOPER access
        await addMemberToProject(newProject.id, gitlabUser.gitlab_user_id, gitlabAccessLevel.DEVELOPER);

        // If the user opted to initialize with a README, prepare and add the README file to the repository
        const readmeContent = await prepareReadmeFileContent(projectSlug);
        await addReadmeFileToRepository(newProject.id, gitlabProductionBranch, readmeContent);

        // Protect the default branch to prevent direct pushes
        await protectBranch(newProject.id, gitlabProductionBranch);

        // Create new development branch from the production branch
        await createGitlabBranch(newProject.id, gitlabDefaultBranch, gitlabProductionBranch);

        // Update default branch of the gitlab project
        await updateProjectDefaultBranch(newProject.id, gitlabDefaultBranch);

        const repositoryObj = {
            description,
            component_draft_id: component_id,
            gitlab_user_id: gitlabUser._id,
            user_id: req.session._id,
            platform_id,
            project_name: projectSlug,
            project_id: newProject.id,
            http_url_to_repo: newProject.http_url_to_repo,
            visibility: visibility,
            default_branch: gitlabDefaultBranch,
            initialize_with_readme,
            is_active: true,
            is_deleted: false,
            state: state,
            published_state: (state === repositoryState.PUBLIC) ? publishState.PUBLISHED : publishState.PRIVATE,
            production_branch_exists: true,
            is_challenge,
            challenge_meta
        };

        // Save repository metadata to the database
        const repositoryData = await GitlabRepository.create(repositoryObj);

        // Create collaborator entry as a owner of repository
        await Collaborator.create({
            user_id: req.session._id,
            gitlab_user_id: gitlabUser.gitlab_user_id,
            gitlab_user_ref: gitlabUser._id,
            repo_id: repositoryData._id,
            access_level: gitlabAccessLevel.OWNER
        });

        if (state == repositoryState.PUBLIC) {
            // Create respective project entry for public code-spaces
            await createComponentForPublicCodeSpace(repositoryData._id);
        }

        // Store main branch content in Redis caching
        await cacheRepositoryDataInRedis(repositoryData._id, newProject.id);

        if (component_id) {
            try {
                // Update the component by adding platform data to the platform_data array
                await DraftComponents.updateOne(
                    { _id: component_id }, // Filter to find the specific component by ID
                    {
                        $push: {
                            platform_data: {
                                platform_id: platform_id,           // ID of the platform to link
                                repository_id: repositoryData._id   // ID of the repository to associate
                            }
                        }
                    }
                );
            } catch (error) {
                // Handle potential errors during the update operation
                console.error('Error updating component with platform data:', error);
            }
        }

        const codeSpaceObj = await GitlabRepository.findOne({ _id: repositoryData._id }).populate({
            'path': 'platform_id',
            'select': 'title slug image_url'
        }).lean();

        // Return a success response with the newly created repository data
        return ReS(res, constants.success_code, 'New repository created successfully', codeSpaceObj);
    } catch (err) {
        // Log the error and return an error response
        logger.error(`Error at Front Controller createNewRepository: ${err}`);
        return sendError(res, err);
    }
}

async function getAllRepository(req, res) {
    try {

        const gitlabUser = await GitlabUsers.findOne({
            user_id: req.session._id
        }, '_id').lean();

        // Check if the GitLab user record was found
        if (gitlabUser == null) {
            // If the GitLab user record does not exist, respond with an error message
            return ReS(res, constants.bad_request_code, 'Oops! Your creator profile does not exists.');
        }

        // Set default conditions
        const conditions = {
            gitlab_user_id: new mongoose.Types.ObjectId(gitlabUser._id),
            is_active: true,
            is_deleted: false
        };

        const totalDocuments = await GitlabRepository.countDocuments(conditions);
        // Set default sort
        const sort = {
            'created_at': -1
        };

        // Check if the link type is 'LINKED'
        if (req.body.linkType == codeSpaceFilterTypes.LINKED) {
            conditions['state'] = repositoryState.PRIVATE;
            conditions['$or'] = [
                { component_id: { $exists: true } },
                { component_draft_id: { $exists: true } }
            ];
        }

        // Check if the link type is 'DETACHED'
        if (req.body.linkType == codeSpaceFilterTypes.DETACHED) {
            conditions['state'] = repositoryState.PRIVATE;
            conditions['component_id'] = {
                $exists: false
            };
            conditions['component_draft_id'] = {
                $exists: false
            };
        }

        // Check if the link type is 'PUBLIC'
        if (req.body.linkType == codeSpaceFilterTypes.PUBLIC) {
            conditions['state'] = repositoryState.PUBLIC;
        }

        // If a 'platform_id' is provided in the request
        if (req.body.platform_id && req.body.platform_id.length) {
            conditions['platform_id'] = {
                $in: (req.body.platform_id).map((id) => new mongoose.Types.ObjectId(id))
            };
        }

        if (req.body.searchText) {
            // Escaping special characters in the dynamic search text
            const escapedSearchText = escapeRegex(req.body.searchText);
            conditions['$or'] = [{
                'project_name': {
                    $regex: escapedSearchText,
                    $options: 'i'
                }
            }];
        }

        const limit = (req.body.limit != undefined) ? req.body.limit : 25;
        const skip = (req.body.skip != undefined) ? req.body.skip : 0;

        const filterDocuments = await GitlabRepository.countDocuments(conditions);
        const query = [{
            $match: conditions
        }, {
            $lookup: {
                from: 'supported_platforms',
                let: {
                    platform_id: '$platform_id'
                },
                pipeline: [{
                    $match: {
                        $expr: {
                            $and: [{
                                $in: ['$_id', '$$platform_id']
                            }]
                        }
                    }
                }, {
                    $project: {
                        'slug': 1,
                        'image_url': 1,
                        'title': 1
                    }
                }],
                as: 'platform_id'
            }
        }, {
            $lookup: {
                from: 'components',
                let: {
                    component_id: '$component_id'
                },
                pipeline: [{
                    $match: {
                        $expr: {
                            $and: [{
                                $eq: ['$_id', '$$component_id']
                            }]
                        }
                    }
                }, {
                    $lookup: {
                        from: 'users', // Join with users collection
                        let: { creatorId: '$created_by_user' },
                        pipeline: [
                            {
                                $match: {
                                    $expr: { $eq: ['$_id', '$$creatorId'] }
                                }
                            },
                            {
                                $project: {
                                    username: 1,
                                    first_name: 1,
                                    last_name: 1,
                                    avatar: 1
                                }
                            }
                        ],
                        as: 'created_by_user'
                    }
                }, {
                    $project: {
                        'slug': 1,
                        'title': 1,
                        'component_state': 1,
                        'component_type': 1,
                        'created_by_user': { $arrayElemAt: ['$created_by_user', 0] }
                    }
                }],
                as: 'component_id'
            }
        }, {
            $lookup: {
                from: 'draft_components',
                let: {
                    component_draft_id: '$component_draft_id'
                },
                pipeline: [{
                    $match: {
                        $expr: {
                            $and: [{
                                $eq: ['$_id', '$$component_draft_id']
                            }]
                        }
                    }
                }, {
                    $lookup: {
                        from: 'users', // Join with users collection
                        let: { creatorId: '$created_by_user' },
                        pipeline: [
                            {
                                $match: {
                                    $expr: { $eq: ['$_id', '$$creatorId'] }
                                }
                            },
                            {
                                $project: {
                                    username: 1,
                                    first_name: 1,
                                    last_name: 1,
                                    avatar: 1
                                }
                            }
                        ],
                        as: 'created_by_user'
                    }
                }, {
                    $project: {
                        'slug': 1,
                        'title': 1,
                        'component_state': 1,
                        'component_type': 1,
                        'created_by_user': { $arrayElemAt: ['$created_by_user', 0] }
                    }
                }],
                as: 'component_draft_id'
            }
        }, {
            $lookup: {
                from: 'gitlab_repositories',
                let: {
                    fork_id: '$fork_id'
                },
                pipeline: [{
                    $match: {
                        $expr: {
                            $and: [{
                                $eq: ['$_id', '$$fork_id']
                            }]
                        }
                    }
                }, {
                    $project: {
                        'project_name': 1,
                        'project_id': 1,
                        'component_state': 1
                    }
                }],
                as: 'fork_id'
            }
        }, {
            $project: {
                project_id: 1,
                project_name: 1,
                state: 1,
                default_branch: 1,
                created_at: 1,
                is_active: 1,
                description: 1,
                component_id: { $arrayElemAt: ['$component_id', 0] },
                component_draft_id: { $arrayElemAt: ['$component_draft_id', 0] },
                fork_id: { $arrayElemAt: ['$fork_id', 0] },
                platform_id: 1,
                stars: 1,
                forks: 1,
                import_status: 1,
                published_state: 1,
                last_activity_at: 1
            }
        }];
        query.push({
            '$sort': sort
        });
        query.push({
            '$skip': skip
        });
        query.push({
            '$limit': limit
        });

        // Aggregate the repository list based on the provided query
        const repositoryList = await GitlabRepository.aggregate(query);

        // Extract the project IDs from the repository list
        const projectIds = repositoryList.map((item) => item.project_id);

        const forkedProjectIds = repositoryList.filter((item) => item?.fork_id).map((item) => item.project_id);

        const syncDifferenceMap = await checkBranchDifferencesForSyncWithFork(forkedProjectIds);

        const projectCommitDifferenceMap = await checkBranchDifferencesForProjects(projectIds);

        // Update the repository list with the last activity date for each project
        repositoryList.forEach((repository, index) => {
            repositoryList[index]['commit_difference'] = projectCommitDifferenceMap[repository.project_id];
            repositoryList[index]['sync_difference'] = syncDifferenceMap[repository.project_id];
        });

        const responseObj = {
            recordsTotal: totalDocuments,
            recordsFiltered: filterDocuments,
            list: repositoryList
        };
        return ReS(res, constants.success_code, 'Data Fetched', responseObj);
    } catch (err) {
        logger.error(`Error at Front Controller getAllRepository${err}`);
        return sendError(res, err);
    }
}

async function getAllRepositorySortList(req, res) {
    try {

        const gitlabUser = await GitlabUsers.findOne({
            user_id: req.session._id
        }, '_id').lean();

        // Check if the GitLab user record was found
        if (gitlabUser == null) {
            // If the GitLab user record does not exist, respond with an error message
            return ReS(res, constants.bad_request_code, 'Oops! Your creator profile does not exists.');
        }

        // Set default conditions
        const conditions = {
            component_id: { $exists: false },
            component_draft_id: { $exists: false },
            gitlab_user_id: new mongoose.Types.ObjectId(gitlabUser._id),
            is_active: true
        };
        // Set default sort
        const sort = {
            'created_at': -1
        };

        // Check if the link type is 'LINKED'
        if (req.query.linkType == codeSpaceFilterTypes.LINKED) {
            conditions['$or'] = [
                { component_id: { $exists: true } },
                { component_draft_id: { $exists: true } }
            ];
        }

        // Check if the link type is 'DETACHED'
        if (req.query.linkType == codeSpaceFilterTypes.DETACHED) {
            conditions['component_id'] = {
                $exists: false
            };
            conditions['component_draft_id'] = {
                $exists: false
            };
            conditions['import_status'] = {
                $ne: gitlabImportStatus.SCHEDULED
            };
        }

        // If a 'platform_id' is provided in the request
        if (req.query.platform_id) {
            conditions['platform_id'] = new mongoose.Types.ObjectId(req.query.platform_id);
        }

        if (req.query.searchText) {
            // Escaping special characters in the dynamic search text
            const escapedSearchText = escapeRegex(req.query.searchText);
            conditions['$or'] = [{
                'project_name': {
                    $regex: escapedSearchText,
                    $options: 'i'
                }
            }];
        }

        // If a 'state' is provided in the request
        if ([repositoryState.PRIVATE, repositoryState.PUBLIC].includes(req.query.state)) {
            conditions['state'] = req.query.state;
        }

        const query = [{
            $match: conditions
        }, {
            $lookup: {
                from: 'supported_platforms',
                let: {
                    platform_id: '$platform_id'
                },
                pipeline: [{
                    $match: {
                        $expr: {
                            $and: [{
                                $in: ['$_id', '$$platform_id']
                            }]
                        }
                    }
                }, {
                    $project: {
                        'title': 1,
                        'slug': 1,
                        'image_url': 1
                    }
                }],
                as: 'platform_id'
            }
        }, {
            $project: {
                project_id: 1,
                project_name: 1,
                state: 1,
                default_branch: 1,
                created_at: 1,
                is_active: 1,
                description: 1,
                http_url_to_repo: 1,
                platform_id: 1,
                stars: 1,
                forks: 1,
                import_status: 1,
                last_activity_at: 1
            }
        }];

        query.push({
            '$sort': sort
        });

        const repositoryList = await GitlabRepository.aggregate(query);



        const updatedRepositoryList = await Promise.all(
            repositoryList.map(async (repository) => {
                // Fetch the repository content and check if the file doesn't exist
                const fileCheck = await fetchRepoContent(repository.project_id);
                repository['file_not_exists'] = fileCheck && fileCheck.file_not_exists === true;

                return repository;
            })
        );

        return ReS(res, constants.success_code, 'Data Fetched', updatedRepositoryList);
    } catch (err) {
        logger.error(`Error at Front Controller getAllRepositorySortList${err}`);
        return sendError(res, err);
    }
}

async function checkRepositoryNameAvailability(req, res) {
    const { project_name } = req.query;
    const isAvailable = await checkProjectNameAvailability(project_name);
    // Return success response with admin profile data
    return ReS(res, constants.success_code, 'Repository name availability successfully.', { isAvailable });
}

async function getRepositoryDetails(req, res) {
    try {
        // Set default conditions
        const conditions = {
            _id: new mongoose.Types.ObjectId(req.params.id)
        };

        const query = [
            { $match: conditions },
            // Join with users collection
            {
                $lookup: {
                    from: 'users',
                    let: { user_id: '$user_id' },
                    pipeline: [
                        {
                            $match: {
                                $expr: { $eq: ['$_id', '$$user_id'] }
                            }
                        },
                        {
                            $project: {
                                first_name: 1,
                                last_name: 1,
                                email: 1,
                                username: 1,
                                avatar: 1,
                                biography: 1
                            }
                        }
                    ],
                    as: 'user_id'
                }
            },
            // Join with components collection
            {
                $lookup: {
                    from: 'components',
                    let: { component_id: '$component_id' },
                    pipeline: [
                        {
                            $match: {
                                $expr: { $eq: ['$_id', '$$component_id'] }
                            }
                        },
                        {
                            $project: {
                                slug: 1,
                                title: 1,
                                component_type: 1,
                                component_state: 1,
                                short_description: 1,
                                long_description: 1,
                                views: 1,
                                likes: 1,
                                bookmarks: 1
                            }
                        }
                    ],
                    as: 'component_id'
                }
            },
            // Join with gitlab_repositories collection (fork info)
            {
                $lookup: {
                    from: 'gitlab_repositories',
                    let: { fork_id: '$fork_id' },
                    pipeline: [
                        {
                            $match: {
                                $expr: { $eq: ['$_id', '$$fork_id'] }
                            }
                        },
                        {
                            $lookup: {
                                from: 'users', // Join with users collection
                                let: { creatorId: '$user_id' },
                                pipeline: [
                                    {
                                        $match: {
                                            $expr: { $eq: ['$_id', '$$creatorId'] }
                                        }
                                    },
                                    {
                                        $project: {
                                            username: 1,
                                            first_name: 1,
                                            last_name: 1,
                                            avatar: 1
                                        }
                                    }
                                ],
                                as: 'created_by_user'
                            }
                        },
                        {
                            $project: {
                                project_id: 1,
                                project_name: 1,
                                state: 1,
                                created_by_user: { $arrayElemAt: ['$created_by_user', 0] },
                                http_url_to_repo: 1
                            }
                        }
                    ],
                    as: 'fork_id'
                }
            },
            // Flatten joined arrays and select final fields
            {
                $project: {
                    project_id: 1,
                    project_name: 1,
                    state: 1,
                    default_branch: 1,
                    created_at: 1,
                    is_active: 1,
                    description: 1,
                    http_url_to_repo: 1,
                    production_branch_exists: 1,
                    initialize_with_readme: 1,
                    gitlab_user_id: 1,
                    state_changes: 1,
                    stars: 1,
                    forks: 1,
                    storage_size: 1,
                    identification_tag: 1,
                    video_url: 1,
                    gitlab_languages: 1,
                    is_forked: 1,
                    user_id: { $arrayElemAt: ['$user_id', 0] },
                    component_id: { $arrayElemAt: ['$component_id', 0] },
                    fork_id: { $arrayElemAt: ['$fork_id', 0] }
                }
            }
        ];

        const repository = await GitlabRepository.aggregate(query);

        if (!repository.length) {
            return ReS(res, constants.resource_not_found, 'Oops! Repository Not Found.');
        }

        const repositoryDetails = repository[0];

        const { project_id, fork_id } = repositoryDetails;

        const tasks = [
            (async () => {
                try {
                    const fileCheck = await fetchRepoContent(project_id);
                    repositoryDetails.file_not_exists = !!fileCheck?.file_not_exists;
                } catch (error) {
                    console.error('Error: fetchRepoContent failed', { project_id, error });
                }
            })()
        ];

        if (fork_id) {
            tasks.push(
                (async () => {
                    try {
                        const syncDifferenceMap = await checkBranchDifferencesForSyncWithFork([project_id]);
                        repositoryDetails.sync_difference = syncDifferenceMap[project_id];
                    } catch (error) {
                        console.error('Error: checkBranchDifferencesForSyncWithFork failed', { project_id, error });
                    }
                })()
            );
        }
        await Promise.allSettled(tasks);
        return ReS(res, constants.success_code, 'Data Fetched', repositoryDetails);
    } catch (err) {
        logger.error(`Error at Front Controller getRepositoryDetails${err}`);
        return sendError(res, err);
    }
}

async function getRepositoryCommitList(req, res) {
    try {
        // Extract repository ID from URL parameters
        const { id } = req.params;

        // Extract query parameters with default values
        const { path = '', ref_name = gitlabDefaultBranch } = req.query;

        // Find the GitLab repository by ID
        const repository = await GitlabRepository.findOne(
            { _id: id, is_active: true }, // Query condition
            'project_id' // Fields to return
        );

        // Handle case where repository is not found
        if (!repository) {
            return ReS(res, constants.resource_not_found, 'Oops! Resource not found.');
        }

        // Fetch commit information from GitLab
        const repositoryData = await fetchCommitList(repository.project_id, path, ref_name);

        // Send the commit information as a JSON response
        return res.status(constants.success_code).json(repositoryData);
    } catch (err) {
        // Log the error and send an error response
        logger.error(`Error in getRepositoryCommitList: ${err}`);
        return sendError(res, err);
    }
}

async function searchRepositoryFiles(req, res) {
    try {
        // Extract repository ID from URL parameters
        const { id } = req.params;

        // Extract query parameters with default values
        const { ref_name = gitlabDefaultBranch } = req.query;

        // Find the GitLab repository by ID
        const repository = await GitlabRepository.findOne(
            { _id: id, is_active: true }, // Query condition
            'project_id' // Fields to return
        );

        // Handle case where repository is not found
        if (!repository) {
            return ReS(res, constants.resource_not_found, 'Oops! Resource not found.');
        }

        // Fetch commit information from GitLab
        try {
            const repositoryData = await findRepositoryFile(repository.project_id, ref_name);
            const blobPaths = repositoryData.filter((item) => item.type === 'blob').map((item) => item.path);
            // Send the commit information as a JSON response
            return res.status(constants.success_code).json(blobPaths);
        } catch {
            return res.status(constants.success_code).json([]);
        }
    } catch (err) {
        // Log the error and send an error response
        logger.error(`Error in searchRepositoryFiles: ${err}`);
        return sendError(res, err);
    }
}

async function getRepositoryLanguages(req, res) {
    try {
        // Extract repository ID from URL parameters
        const { id } = req.params;

        // Find the GitLab repository by ID
        const repository = await GitlabRepository.findOne(
            { _id: id, is_active: true }, // Query condition
            'project_id' // Fields to return
        );

        // Handle case where repository is not found
        if (!repository) {
            return ReS(res, constants.resource_not_found, 'Oops! Resource not found.');
        }

        // Fetch commit information from GitLab
        const repositoryData = await fetchProjectLanguages(repository.project_id);

        // Send the commit information as a JSON response
        return res.status(constants.success_code).json(repositoryData);
    } catch (err) {
        // Log the error and send an error response
        logger.error(`Error in getRepositoryLanguages: ${err}`);
        return sendError(res, err);
    }
}

async function createRepositoryFork(req, res) {
    try {
        // Destructure required fields from the request body
        const { project_name, description, platform_id, state = repositoryState.PRIVATE } = req.body;
        const { id } = req.params;

        // Generate a URL-friendly slug from the project name
        const projectSlug = generateCodeSpaceSlug(project_name);

        // Fetch GitLab user information based on the admin ID from the session
        const gitlabUser = await GitlabUsers.findOne({ user_id: req.session._id }).lean();

        // If the GitLab user record is not found, return an error response
        if (!gitlabUser) {
            return ReS(res, constants.bad_request_code, 'Oops! Your creator profile does not exist.');
        }

        // Find repository metadata from the database
        const repository = await GitlabRepository.findOne({ _id: id, is_active: true }).populate({
            path: 'gitlab_user_id',
            select: 'user_id'
        }).lean();

        // Handle case where repository is not found
        if (!repository) {
            return ReS(res, constants.resource_not_found, 'Oops! Resource not found.');
        }

        if (!repository.production_branch_exists) {
            return ReS(res, constants.resource_not_found, 'Oops! Component repository not Published.');
        }

        if (repository?.gitlab_user_id?.user_id.toString() == req.session._id.toString()) {
            return ReS(res, constants.bad_request_code, 'Oops! As the author of this code-space, you\'re not allowed to fork it.');
        }

        const forkCodeSpace = await GitlabRepository.findOne({ fork_id: id, gitlab_user_id: gitlabUser._id, is_active: true }).select('_id fork_id user_id gitlab_user_id').lean();

        if (forkCodeSpace) {
            return ReS(res, constants.bad_request_code, 'Oops! A fork of this code-space already exists.');
        }

        // Decide project visibility based on provided state
        const visibility = (state === repositoryState.PUBLIC) ? projectVisibility.INTERNAL : projectVisibility.PRIVATE;

        const forkedRepository = await createProjectFork(repository.project_id, projectSlug, gitlabProductionBranch, description, visibility);

        // Save repository metadata to the database
        const repositoryData = await GitlabRepository.create({
            description: description,
            gitlab_user_id: gitlabUser._id,
            user_id: req.session._id,
            platform_id: platform_id,
            project_name: projectSlug,
            project_id: forkedRepository.id,
            http_url_to_repo: forkedRepository.http_url_to_repo,
            visibility: visibility,
            default_branch: gitlabDefaultBranch,
            initialize_with_readme: repository.initialize_with_readme,
            is_active: true,
            is_deleted: false,
            import_status: forkedRepository.import_status,
            fork_id: id,
            is_forked: true,
            state: state,
            published_state: (state === repositoryState.PUBLIC) ? publishState.PUBLISHED : publishState.PRIVATE,
            gitlab_languages: repository?.gitlab_languages,
            detected_languages: repository?.detected_languages,
            detected_platforms: repository?.detected_platforms,
            is_submission: (repository?.is_challenge === true) ? true : false
        });

        // Create collaborator entry as a owner of repository
        await Collaborator.create({
            user_id: req.session._id,
            gitlab_user_id: gitlabUser.gitlab_user_id,
            gitlab_user_ref: gitlabUser._id,
            repo_id: repositoryData._id,
            access_level: gitlabAccessLevel.OWNER
        });

        // Increased fork count in source repository
        await GitlabRepository.updateOne({
            _id: id
        }, {
            $inc: {
                forks: 1
            }
        });
        // Return a success response with the newly created repository data
        return ReS(res, constants.success_code, 'Repository fork scheduled successfully', repositoryData);
    } catch (err) {
        // Log the error and return an error response
        logger.error(`Error at Front Controller createNewRepository: ${err}`);
        return sendError(res, err);
    }
}

async function publishPublicRepository(req, res) {
    try {
        const { id } = req.params; // Extract repository ID from request parameters

        // Fetch the repository with the required details
        const repository = await GitlabRepository.findOne({
            _id: id,
            state: repositoryState.PUBLIC,
            is_active: true
        }).populate({ path: 'gitlab_user_id', select: 'user_id' }).lean();

        // Handle case where repository is not found
        if (!repository) {
            return ReS(res, constants.resource_not_found, 'Repository not found or already private.');
        }

        // Validate if the requester is the repository owner
        if (repository.gitlab_user_id.user_id.toString() !== req.session._id.toString()) {
            return ReS(res, constants.forbidden_code, 'Unauthorized: Only the repository owner can publish.');
        }

        // Publish the repository
        await publishComponentRepository([repository._id]);

        // Update repository publish status
        await GitlabRepository.updateOne(
            { _id: id },
            { $set: { published_state: publishState.PUBLISHED } }
        );

        return ReS(res, constants.success_code, 'Public repository published successfully.');
    } catch (err) {
        logger.error(`Error in publishPublicRepository: ${err.message}`);
        return sendError(res, err);
    }
}

async function changeCodeSpaceVisibility(req, res) {
    try {
        const { id } = req.params; // Extract repository ID from request parameters
        const { state } = req.body; // Extract desired state from request body
        const userId = req.session._id.toString(); // Get user ID from session

        // Fetch repository with required details
        const repository = await GitlabRepository.findOne({ _id: id, is_active: true })
            .populate({ path: 'gitlab_user_id', select: 'user_id' })
            .lean();

        // If repository is not found, return an error response
        if (!repository) {
            return ReS(res, constants.resource_not_found, 'Codespace not found.');
        }

        // Validate if the requester is the repository owner
        if (repository.gitlab_user_id?.user_id.toString() !== userId) {
            return ReS(res, constants.forbidden_code, 'Unauthorized: Only the codespace owner can change the visibility.');
        }

        // If requested visibility is already set, return early
        if (state === repository.state) {
            return ReS(res, constants.bad_request_code, `Codespace is already ${state.toLowerCase()}.`);
        }

        // Decide project visibility based on provided state
        const visibility = (state === repositoryState.PUBLIC) ? projectVisibility.INTERNAL : projectVisibility.PRIVATE;

        if (state === repositoryState.PUBLIC) {
            // Prevent changing visibility to public if repository is linked to a project
            if ('component_id' in repository || 'component_draft_id' in repository) {
                return ReS(res, constants.bad_request_code, 'Codespace is linked to a project and cannot be made public.');
            }

            // Update project visibility on Gitlab
            await updateProjectVisibility(repository.project_id, visibility);
            // Create respective project entry for public codespace
            await createComponentForPublicCodeSpace(repository._id);
            // Cache main branch content in Redis
            await cacheRepositoryDataInRedis(repository._id, repository.project_id);
            // Update repository state to public
            await GitlabRepository.updateOne({ _id: id }, {
                $set: {
                    state: repositoryState.PUBLIC,
                    published_state: publishState.PUBLISHED,
                    visibility: visibility,
                    forks: 0
                }
            });
            // Remove all fork links related to this code space
            await GitlabRepository.updateMany({
                fork_id: id
            }, {
                $set: {
                    is_forked: false
                },
                $unset: { fork_id: 1 }
            });
        } else if (state === repositoryState.PRIVATE) {
            // Update project visibility on Gitlab
            await updateProjectVisibility(repository.project_id, visibility);
            // Fetch public project associated with this repository
            const publicProject = await Components.findOne({
                public_repository_id: id, component_type: componentType.CODESPACE
            }).lean();

            // Prepare repository update object
            const repositoryObj = {
                state: repositoryState.PRIVATE,
                published_state: publishState.PRIVATE,
                visibility: visibility,
                forks: 0,
                ...(publicProject && { public_component_id: publicProject._id }) // Add public component ID if exists
            };

            // Update repository state to private and remove project associations
            await GitlabRepository.updateOne({ _id: id }, {
                $set: repositoryObj,
                $unset: { component_id: 1, component_draft_id: 1 }
            });

            // Remove all fork links related to this code space
            await GitlabRepository.updateMany({
                fork_id: id
            }, {
                $set: {
                    is_forked: false
                },
                $unset: { fork_id: 1 }
            });
            // Update state of linked public project to active draft
            await Components.updateOne({
                public_repository_id: id,
                component_type: componentType.CODESPACE
            }, {
                $set: { component_state: componentState.ACTIVE_DRAFT }
            });
        }

        return ReS(res, constants.success_code, 'Codespace visibility updated successfully.');
    } catch (err) {
        logger.error(`Error in changeCodeSpaceVisibility: ${err.message}`);
        return sendError(res, err);
    }
}

async function updateCodeSpaceDetails(req, res) {
    try {
        const { id } = req.params; // Extract repository ID from request parameters
        const { identification_tag, video_url } = req.body; // Extract desired state from request body
        const userId = req.session._id.toString(); // Get user ID from session

        // Fetch repository with required details
        const repository = await GitlabRepository.findOne({ _id: id })
            .populate({ path: 'gitlab_user_id', select: 'user_id' })
            .lean();

        // If repository is not found, return an error response
        if (!repository) {
            return ReS(res, constants.resource_not_found, 'Codespace not found.');
        }

        // Validate if the requester is the repository owner
        if (repository.gitlab_user_id?.user_id.toString() !== userId) {
            return ReS(res, constants.forbidden_code, 'Unauthorized: Only the codespace owner can change the details.');
        }

        await GitlabRepository.updateOne({ _id: id }, {
            $set: {
                identification_tag: identification_tag,
                video_url: video_url
            }
        });
        return ReS(res, constants.success_code, 'Codespace updated successfully.');
    } catch (err) {
        logger.error(`Error in updateCodeSpaceDetails: ${err.message}`);
        return sendError(res, err);
    }
}

async function createForkSyncMergeRequest(req, res) {
    try {
        const { id } = req.params;
        const userId = req.session._id.toString();

        // Fetch the forked repository and ensure it has a fork_id
        const repository = await GitlabRepository.findOne({
            _id: id,
            fork_id: { $exists: true }
        }).populate({ path: 'gitlab_user_id', select: 'user_id' }).lean();

        if (!repository) {
            return ReS(res, constants.resource_not_found, 'Oops! Component repository not found.');
        }

        // Ensure the requester is the owner of the repository
        const repoOwnerId = repository.gitlab_user_id?.user_id?.toString();
        if (repoOwnerId !== userId) {
            return ReS(res, constants.forbidden_code, 'Unauthorized: Only the repository owner can sync fork.');
        }

        // Fetch the upstream/original project (where fork originated from)
        const forkCodeSpace = await GitlabRepository.findOne({ _id: repository.fork_id }).lean();
        if (!forkCodeSpace) {
            return ReS(res, constants.bad_request_code, 'Oops! Upstream repository not found.');
        }

        // Extract namespace from repository URL (e.g., group/repo)
        // eslint-disable-next-line no-useless-escape
        const match = forkCodeSpace.http_url_to_repo.match(/\/([^\/]+\/[^\/.]+)\.git$/);
        const nameSpace = match?.[1];
        if (!nameSpace) {
            return ReS(res, constants.bad_request_code, 'Invalid repository URL format.');
        }

        const sourceBranch = `${nameSpace}:${gitlabProductionBranch}`;
        const title = `Merge ${sourceBranch} into ${gitlabDefaultBranch}`;

        // Create the merge request using fork source and upstream target
        const { iid, message } = await createGitLabMergeRequestWithFork(
            repository.project_id,         // target (upstream) project ID
            gitlabProductionBranch,        // source branch
            gitlabDefaultBranch,           // target branch
            title,
            '',                             // optional description
            forkCodeSpace.project_id       // source (fork) project ID
        );

        return ReS(res, constants.success_code, message, { id: id, iid: parseInt(iid) });
    } catch (err) {
        logger.error(`Error at Front Controller createForkSyncMergeRequest: ${err}`);
        return sendError(res, err);
    }
}

async function fetchCodeSpaceTagSuggestions(req, res) {
    try {
        // Extract repository ID from URL parameters
        const { id } = req.params;
        // Find the GitLab repository by ID
        const repository = await GitlabRepository.findOne(
            { _id: id }, // Query condition
            'project_id' // Fields to return
        );

        // Handle case where repository is not found
        if (!repository) {
            return ReS(res, constants.resource_not_found, 'Oops! Resource not found.');
        }

        // Fetch commit information from GitLab
        const languages = await fetchProjectLanguages(repository.project_id);

        // // Extract unique language keys
        const uniqueLanguages = Object.keys(languages);

        // Send the commit information as a JSON response
        return ReS(res, constants.success_code, 'Component tags fetched successfully', { tags: uniqueLanguages });
    } catch (err) {
        // Log the error and send an error response
        logger.error(`Error in fetchCodeSpaceTagSuggestions: ${err}`);
        return sendError(res, err);
    }
}

async function fetchRepositoryForksList(req, res) {
    try {
        const { id } = req.params; // Extract repository ID from request parameters
        const userId = req.session._id.toString(); // Get user ID from session

        // Fetch repository with required details
        const repository = await GitlabRepository.findOne({ _id: id, user_id: userId }).lean();

        // If repository is not found, return an error response
        if (!repository) {
            return ReS(res, constants.resource_not_found, 'Codespace not found.');
        }

        // Set default conditions
        const conditions = {
            fork_id: new mongoose.Types.ObjectId(id),
            is_active: true
        };

        // Set default sort
        const sort = {
            created_at: -1
        };

        const totalDocuments = await GitlabRepository.countDocuments(conditions);

        const limit = (req.body.limit != undefined) ? req.body.limit : 10;
        const skip = (req.body.skip != undefined) ? req.body.skip : 0;

        const filterDocuments = await GitlabRepository.countDocuments(conditions);

        const query = [{
            $match: conditions
        }, {
            $lookup: {
                from: 'users',
                let: { user_id: '$user_id' },
                pipeline: [
                    {
                        $match: {
                            $expr: { $eq: ['$_id', '$$user_id'] }
                        }
                    },
                    {
                        $project: {
                            first_name: 1,
                            last_name: 1,
                            email: 1,
                            username: 1,
                            avatar: 1,
                            biography: 1
                        }
                    }
                ],
                as: 'user_id'
            }
        },
        // Join with components collection
        {
            $lookup: {
                from: 'components',
                let: { component_id: '$component_id' },
                pipeline: [
                    {
                        $match: {
                            $expr: { $eq: ['$_id', '$$component_id'] }
                        }
                    },
                    {
                        $project: {
                            views: 1,
                            likes: 1,
                            bookmarks: 1
                        }
                    }
                ],
                as: 'component_id'
            }
        }, {
            $project: {
                project_id: 1,
                project_name: 1,
                state: 1,
                created_at: 1,
                description: 1,
                fork_id: 1,
                forks: 1,
                import_status: 1,
                last_activity_at: 1,
                gitlab_languages: 1,
                user_id: { $arrayElemAt: ['$user_id', 0] },
                component_id: { $arrayElemAt: ['$component_id', 0] }
            }
        }];

        query.push({
            '$sort': sort
        });
        query.push({
            '$skip': skip
        });
        query.push({
            '$limit': limit
        });

        // Aggregate the repository list based on the provided query
        const forkedList = await GitlabRepository.aggregate(query);

        const responseObj = {
            recordsTotal: totalDocuments,
            recordsFiltered: filterDocuments,
            list: forkedList
        };

        return ReS(res, constants.success_code, 'Data Fetched', responseObj);
    } catch (err) {
        logger.error(`Error in fetchRepositoryForksList: ${err.message}`);
        return sendError(res, err);
    }
}

async function deleteCodeSpace(req, res) {
    try {
        const { id } = req.params; // Extract repository ID from request parameters
        const userId = req.session._id.toString(); // Get user ID from session

        // Fetch repository with required details
        const repository = await GitlabRepository.findOne({ _id: id }).lean();

        // If repository is not found, return an error response
        if (!repository) {
            return ReS(res, constants.resource_not_found, 'Codespace not found.');
        }

        // Validate if the requester is the repository owner
        if (repository.user_id.toString() !== userId) {
            return ReS(res, constants.forbidden_code, 'Unauthorized: Only the codespace owner can change the visibility.');
        }

        const deleteCheck = await isCodespaceDeletable(repository);

        // Check if there's an error and send the response
        if (deleteCheck && deleteCheck.error) {
            return ReS(res, deleteCheck.status, deleteCheck.error);
        }
        // Remove project from the gitlab
        await deleteGitlabProject(repository?.project_id);

        // Resync storage used by user after codespace deletion
        await fetchAndSyncUserUsedStorage(repository?.project_id);

        // If this repository is a fork, decrement the fork count on the original (source) repository
        if (repository?.fork_id) {
            await GitlabRepository.updateOne({
                _id: repository?.fork_id
            }, {
                $inc: {
                    forks: -1
                }
            });
        }
        // Remove project as well linked with public codespace
        if (repository?.state == repositoryState.PUBLIC) {
            await Components.updateOne({
                public_repository_id: repository._id
            }, {
                $set: {
                    component_state: componentState.SOFT_DELETED
                }
            });
        }
        // Remove codespace from the database
        await GitlabRepository.updateOne({
            _id: id
        }, {
            $set: {
                is_deleted: true,
                is_active: false
            }
        });
        // Remove all fork links related to this code space
        await GitlabRepository.updateMany({
            fork_id: id
        }, {
            $set: {
                is_forked: false
            },
            $unset: {
                fork_id: 1
            }
        });
        return ReS(res, constants.success_code, 'Codespace deleted successfully.');
    } catch (err) {
        logger.error(`Error in deleteCodeSpace: ${err.message}`);
        return sendError(res, err);
    }
}

module.exports = {
    createNewRepository,
    getAllRepository,
    getAllRepositorySortList,
    checkRepositoryNameAvailability,
    getRepositoryDetails,
    getRepositoryCommitList,
    searchRepositoryFiles,
    getRepositoryLanguages,
    createRepositoryFork,
    publishPublicRepository,
    changeCodeSpaceVisibility,
    updateCodeSpaceDetails,
    createForkSyncMergeRequest,
    fetchCodeSpaceTagSuggestions,
    fetchRepositoryForksList,
    deleteCodeSpace
};