// Npm declaration
const mongoose = require('mongoose');

// Models declaration
const DraftChallenges = require('../../models/draft_challenges.model').DraftChallenges;
const Challenges = require('../../models/challenges.model').Challenges;
const Category = require('../../models/category.model').Category;

// Service declaration
const constants = require('../../config/constants');
const { ReS, sendError, generateSlug } = require('../../services/general.helper');
const logger = require('../../config/logger');
const { componentType, challengeState } = require('../../config/component.constant');
const { checkAndManageChallengeState } = require('../../services/component.service');
const { publishChallengeValidation } = require('../../validations/front/challenge/challengeValidation');

async function createChallengePlaceHolder(req, res) {
    try {
        const { title = 'Untitled', challenge_type = componentType.ELEMENTS, category_id } = req.body;

        const challengeSlug = generateSlug(title);

        const challengeId = new mongoose.Types.ObjectId();

        if (category_id) {
            const categoryData = await Category.findOne({ _id: category_id }, '_id').lean();

            if (!categoryData) {
                return ReS(res, constants.resource_not_found, 'Oops! Category Not Found.');
            }
        }

        const newChallenge = await DraftChallenges.create({
            _id: challengeId,
            title: title,
            slug: challengeSlug,
            component_state: challengeState.PLACEHOLDER,
            created_by_user: req.session._id,
            challenge_type: challenge_type,
            category_id: category_id
        });

        return ReS(res, constants.success_code, 'Success', {
            challenge_id: newChallenge._id
        });
    } catch (err) {
        logger.error(`Error at Front Controller createChallengePlaceHolder ${err}`);
        return sendError(res, err);
    }
}

async function updateChallenge(req, res) {
    try {
        const postData = req.body;
        // Fetch the challenge data based on the component ID from the request parameters
        const challenge = await DraftChallenges.findOne({
            _id: req.params.id
        }, 'component_type component_state').lean();

        // Check if the challenge exists
        if (!challenge) {
            return ReS(res, constants.resource_not_found, 'Oops! Challenge Not Found.');
        }

        // Generate a slug if the title is provided in postData
        if (postData.title) {
            postData.slug = generateSlug(postData.title);
        }

        const isStateChanged = await checkAndManageChallengeState(req.params.id, postData);

        if (isStateChanged) {
            postData.challenge_state = challengeState.ACTIVE_DRAFT;
        }

        // Set the 'updated_by_user' field to the current session's
        postData.updated_by_user = req.session._id;

        const updateQuery = { '$set': postData };

        // Update the challenge with the new data
        await DraftChallenges.updateOne({ _id: req.params.id }, updateQuery);

        // Respond with success
        return ReS(res, constants.success_code, 'Success');
    } catch (err) {
        logger.error(`Error at Front Controller updateChallenge: ${err}`);
        return sendError(res, err);
    }
}

async function getDraftChallengeDetails(req, res) {
    try {
        // Set default conditions
        const conditions = {
            '_id': req.params.id,
            created_by_user: req.session._id
        };

        const challenge = await DraftChallenges.findOne(conditions)
            .populate({
                'path': 'category_id',
                'select': 'category_name category_slug image_url'
            }).populate({
                'path': 'created_by_user',
                'select': 'first_name last_name username email avatar'
            }).populate({
                'path': 'reference_id',
                'select': 'title component_type component_state'
            }).lean();

        if (!challenge) {
            return ReS(res, constants.resource_not_found, 'Oops! Challenge Not Found.');
        }

        return ReS(res, constants.success_code, 'Data Fetched', challenge);
    } catch (err) {
        logger.error(`Error at Front Controller getDraftChallengeDetails ${err}`);
        return sendError(res, err);
    }
}

async function publishDraftChallenges(req, res) {
    try {
        const challengeId = req.params.id;

        const conditions = {
            _id: challengeId,
            created_by_user: req.session._id
        };

        // Fetch the challenge by ID with selected fields
        const challenge = await DraftChallenges.findOne(conditions).lean();

        // Return an error response if the challenge is not found
        if (!challenge) {
            return ReS(res, constants.resource_not_found, 'Oops! Challenge Not Found.');
        }

        // Validate challenge data before publishing
        const { error } = publishChallengeValidation(challenge);
        if (error) {
            return ReS(res, constants.bad_request_code, error.details[0].message);
        }

        // Check for a unique slug across non-placeholder challenge
        const existingChallenge = await Challenges.findOne({
            slug: challenge.slug,
            challenge_draft_id: { $ne: challenge._id }
        }).lean();

        // Return an error if a challenge with the same slug already exists
        if (existingChallenge) {
            return ReS(res, constants.conflict_code, `Oops! Challenge with title ${challenge.title} already published.`);
        }

        // Prepare challenge data object that would be transferred from daft to published
        const challengeObj = {
            title: challenge?.title,
            slug: challenge?.slug,
            description: challenge?.description,
            image_url: challenge?.image_url,
            created_by_user: challenge?.created_by_user,
            updated_by_user: challenge?.updated_by_user,
            category_id: challenge?.category_id,
            challenge_type: challenge?.challenge_type,
            start_date: challenge?.start_date,
            end_date: challenge?.end_date,
            result_date: challenge?.result_date,
            reference_id: challenge?.reference_id,
            challenge_state: challengeState.PUBLISHED,
            identification_tag: challenge?.identification_tag
        };

        // Update the component's state to published
        const publishedChallenge = await Challenges.findOneAndUpdate({
            challenge_draft_id: challenge._id
        }, {
            $set: challengeObj,
            $setOnInsert: {
                createdOn: new Date()
            },
            $inc: {
                published_count: 1
            }
        }, {
            upsert: true,
            new: true
        }).lean();

        // Updated draft component status to published
        await DraftChallenges.updateOne({
            _id: challenge._id
        }, {
            $set: {
                challenge_state: challengeState.PUBLISHED
            }
        });
        // Return a success response
        return ReS(res, constants.success_code, 'Challenge published successfully', { _id: publishedChallenge._id });
    } catch (err) {
        // Log and handle any errors
        logger.error(`Error at Front Controller publishDraftChallenges: ${err}`);
        return sendError(res, err);
    }
}

module.exports = {
    createChallengePlaceHolder,
    updateChallenge,
    getDraftChallengeDetails,
    publishDraftChallenges
};